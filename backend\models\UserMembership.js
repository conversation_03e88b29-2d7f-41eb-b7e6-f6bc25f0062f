const mongoose = require('mongoose');
const { Schema } = mongoose;

const UserMembershipSchema = new mongoose.Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  
  // Current active tier (1-4)
  tier: {
    type: Number,
    enum: [0, 1, 2, 3, 4], // 0 = no membership, 1-4 = tiers
    default: 0
  },
  
  // Subscription details
  subscriptionType: {
    type: String,
    enum: ['monthly', 'one_time'],
    default: 'monthly'
  },
  
  // Payment provider
  paymentProvider: {
    type: String,
    enum: ['paypal', 'coinbase', 'square'],
    required: function() { return this.tier > 0; }
  },
  
  // Provider-specific IDs
  paypalSubscriptionId: String,
  paypalPaymentId: String,
  coinbaseChargeId: String,
  coinbasePaymentId: String,
  squarePaymentId: String,
  squareOrderId: String,
  
  // Subscription status
  subscriptionStatus: {
    type: String,
    enum: ['active', 'cancelled', 'expired', 'pending', 'failed'],
    default: 'pending'
  },
  
  // Payment amount (monthly fee in cents)
  monthlyAmount: {
    type: Number,
    default: 0
  },
  currency: {
    type: String,
    default: 'USD'
  },
  
  // Tier details
  tierName: {
    type: String,
    enum: ['None', 'Forest Guardian', 'Mountain Warrior', 'Arcane Master', 'Dragon Lord'],
    default: 'None'
  },
  
  // Unlocked profile images
  unlockedImages: [{
    type: String,
    enum: ['elf.png', 'dwarf.png', 'mage.png', 'dragon.png']
  }],
  
  // Subscription dates
  subscriptionStartDate: Date,
  subscriptionEndDate: Date,
  nextBillingDate: Date,
  lastPaymentDate: Date,
  currentPeriodStart: Date,
  currentPeriodEnd: Date,
  
  // Pending tier changes (for downgrades)
  pendingTierChange: {
    newTier: {
      type: Number,
      enum: [0, 1, 2, 3, 4]
    },
    effectiveDate: Date,
    reason: {
      type: String,
      enum: ['downgrade', 'cancellation']
    },
    scheduledBy: Date
  },
  
  // Cancellation info
  cancelledAt: Date,
  cancellationReason: String,
  willCancelAt: Date, // When subscription will actually end (after current period)
  
  // Grace period for failed payments
  gracePeriodEndDate: Date,
  
  // Beta access (removed - now production ready)
  betaAccess: {
    type: Boolean,
    default: false // Production mode - users need to pay for access
  },
  
  // Status flags
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Payment history
  paymentHistory: [{
    date: { type: Date, default: Date.now },
    amount: Number,
    provider: String,
    transactionId: String,
    status: { type: String, enum: ['success', 'failed', 'pending', 'refunded'] },
    type: { type: String, enum: ['subscription', 'upgrade', 'downgrade', 'one_time', 'prorated_upgrade'] },
    fromTier: Number,
    toTier: Number,
    periodStart: Date,
    periodEnd: Date,
    prorationDetails: {
      daysRemaining: Number,
      proratedAmount: Number,
      fullAmount: Number
    }
  }],
  
  // Additional data
  metadata: {
    type: Map,
    of: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Index for fast lookups
UserMembershipSchema.index({ user: 1 });
UserMembershipSchema.index({ tier: 1 });
UserMembershipSchema.index({ isActive: 1 });

// Virtual for tier benefits and pricing
UserMembershipSchema.virtual('tierBenefits').get(function() {
  const benefits = {
    0: { images: [], name: 'None', monthlyPrice: 0, oneTimePrice: 0 },
    1: { images: ['elf.png'], name: 'Forest Guardian', monthlyPrice: 500, oneTimePrice: 1500 }, // $5/month or $15 one-time
    2: { images: ['elf.png', 'dwarf.png'], name: 'Mountain Warrior', monthlyPrice: 1000, oneTimePrice: 3000 }, // $10/month or $30 one-time
    3: { images: ['elf.png', 'dwarf.png', 'mage.png'], name: 'Arcane Master', monthlyPrice: 2000, oneTimePrice: 6000 }, // $20/month or $60 one-time
    4: { images: ['elf.png', 'dwarf.png', 'mage.png', 'dragon.png'], name: 'Dragon Lord', monthlyPrice: 4000, oneTimePrice: 12000 } // $40/month or $120 one-time
  };
  return benefits[this.tier] || benefits[0];
});

// Method to check if subscription is currently active
UserMembershipSchema.methods.isSubscriptionActive = function() {
  const now = new Date();
  
  // For existing users: if they have a tier and isActive is true, consider them active
  // This handles the transition from beta to production
  if (this.tier > 0 && this.isActive) {
    return true;
  }
  
  // Check if subscription is active and not expired
  if (this.subscriptionStatus === 'active' && this.isActive) {
    // If there's a current period end, check if we're still in it
    if (this.currentPeriodEnd && now <= this.currentPeriodEnd) {
      return true;
    }
    // If no period end set, check subscription end date
    if (!this.subscriptionEndDate || this.subscriptionEndDate > now) {
      return true;
    }
  }
  
  // Check if cancelled but still in grace period
  if (this.subscriptionStatus === 'cancelled' && this.willCancelAt && now < this.willCancelAt) {
    return true;
  }
  
  return false;
};

// Method to check if user has access to specific image
UserMembershipSchema.methods.hasImageAccess = function(imageName) {
  // Check if subscription is active and tier includes this image
  if (!this.isSubscriptionActive()) {
    return false;
  }
  
  return this.unlockedImages.includes(imageName);
};

// Method to get all accessible images
UserMembershipSchema.methods.getAccessibleImages = function() {
  if (!this.isSubscriptionActive()) {
    return [];
  }
  
  return this.unlockedImages;
};

// Method to upgrade/downgrade subscription
UserMembershipSchema.methods.changeTier = async function(newTier, paymentProvider, paymentData) {
  const tierBenefits = this.constructor.getTierBenefits(newTier);
  const oldTier = this.tier;
  
  this.tier = newTier;
  this.tierName = tierBenefits.name;
  this.unlockedImages = tierBenefits.images;
  this.paymentProvider = paymentProvider;
  this.lastPaymentDate = new Date();
  
  // Add to payment history
  this.paymentHistory.push({
    date: new Date(),
    amount: this.monthlyAmount,
    provider: paymentProvider,
    transactionId: paymentData.transactionId,
    status: 'success',
    type: newTier > oldTier ? 'upgrade' : (newTier < oldTier ? 'downgrade' : 'subscription')
  });
  
  // Update subscription dates
  if (this.subscriptionType === 'monthly') {
    this.nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
  }
  
  await this.save();
  return this;
};

// Method to calculate prorated amount for upgrades
UserMembershipSchema.methods.calculateProratedUpgrade = function(newTier) {
  const now = new Date();
  const currentTierBenefits = this.constructor.getTierBenefits(this.tier);
  const newTierBenefits = this.constructor.getTierBenefits(newTier);
  
  if (newTier <= this.tier) {
    return { amount: 0, daysRemaining: 0 }; // No charge for downgrades
  }
  
  // Calculate days remaining in current period
  const periodEnd = this.currentPeriodEnd || this.nextBillingDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  const daysRemaining = Math.max(0, Math.ceil((periodEnd - now) / (24 * 60 * 60 * 1000)));
  
  // Calculate prorated difference
  const priceDifference = newTierBenefits.monthlyPrice - currentTierBenefits.monthlyPrice;
  const proratedAmount = Math.round((priceDifference * daysRemaining) / 30);
  
  return {
    amount: proratedAmount,
    daysRemaining,
    priceDifference,
    currentPrice: currentTierBenefits.monthlyPrice,
    newPrice: newTierBenefits.monthlyPrice
  };
};

// Method to start a new 30-day subscription
UserMembershipSchema.methods.startSubscription = async function(tier, paymentData) {
  const now = new Date();
  const periodEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
  const tierBenefits = this.constructor.getTierBenefits(tier);
  
  this.tier = tier;
  this.tierName = tierBenefits.name;
  this.unlockedImages = tierBenefits.images;
  this.subscriptionType = 'monthly';
  this.subscriptionStatus = 'active';
  this.subscriptionStartDate = now;
  this.currentPeriodStart = now;
  this.currentPeriodEnd = periodEnd;
  this.nextBillingDate = periodEnd;
  this.lastPaymentDate = now;
  this.monthlyAmount = tierBenefits.monthlyPrice;
  this.isActive = true;
  
  // Clear any pending changes
  this.pendingTierChange = undefined;
  this.cancelledAt = undefined;
  this.willCancelAt = undefined;
  
  // Add to payment history
  this.paymentHistory.push({
    date: now,
    amount: tierBenefits.monthlyPrice,
    provider: paymentData.provider,
    transactionId: paymentData.transactionId,
    status: 'success',
    type: 'subscription',
    toTier: tier,
    periodStart: now,
    periodEnd: periodEnd
  });
  
  await this.save();
  return this;
};

// Method to upgrade subscription immediately (with prorated charge)
UserMembershipSchema.methods.upgradeSubscription = async function(newTier, paymentData) {
  if (newTier <= this.tier) {
    throw new Error('Cannot upgrade to a lower or equal tier');
  }
  
  const proration = this.calculateProratedUpgrade(newTier);
  const now = new Date();
  const tierBenefits = this.constructor.getTierBenefits(newTier);
  
  // Update tier immediately
  const oldTier = this.tier;
  this.tier = newTier;
  this.tierName = tierBenefits.name;
  this.unlockedImages = tierBenefits.images;
  this.monthlyAmount = tierBenefits.monthlyPrice;
  this.lastPaymentDate = now;
  
  // Add to payment history
  this.paymentHistory.push({
    date: now,
    amount: proration.amount,
    provider: paymentData.provider,
    transactionId: paymentData.transactionId,
    status: 'success',
    type: 'prorated_upgrade',
    fromTier: oldTier,
    toTier: newTier,
    periodStart: this.currentPeriodStart,
    periodEnd: this.currentPeriodEnd,
    prorationDetails: proration
  });
  
  await this.save();
  return this;
};

// Method to schedule a downgrade (takes effect at period end)
UserMembershipSchema.methods.scheduleDowngrade = async function(newTier, reason = 'User requested downgrade') {
  if (newTier >= this.tier) {
    throw new Error('Cannot downgrade to a higher or equal tier');
  }
  
  const periodEnd = this.currentPeriodEnd || this.nextBillingDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  
  this.pendingTierChange = {
    newTier: newTier,
    effectiveDate: periodEnd,
    reason: 'downgrade',
    scheduledBy: new Date()
  };
  
  await this.save();
  return this;
};

// Method to cancel subscription (keeps access until period end)
UserMembershipSchema.methods.cancelSubscription = async function(reason = 'User requested') {
  const now = new Date();
  const periodEnd = this.currentPeriodEnd || this.nextBillingDate || new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  
  this.subscriptionStatus = 'cancelled';
  this.cancelledAt = now;
  this.cancellationReason = reason;
  this.willCancelAt = periodEnd;
  
  // Schedule downgrade to tier 0 at period end
  this.pendingTierChange = {
    newTier: 0,
    effectiveDate: periodEnd,
    reason: 'cancellation',
    scheduledBy: now
  };
  
  await this.save();
  return this;
};

// Method to process pending tier changes (called by scheduler)
UserMembershipSchema.methods.processPendingChange = async function() {
  if (!this.pendingTierChange || !this.pendingTierChange.effectiveDate) {
    return false;
  }
  
  const now = new Date();
  if (now < this.pendingTierChange.effectiveDate) {
    return false; // Not time yet
  }
  
  const oldTier = this.tier;
  const newTier = this.pendingTierChange.newTier;
  const tierBenefits = this.constructor.getTierBenefits(newTier);
  
  // Apply the tier change
  this.tier = newTier;
  this.tierName = tierBenefits.name;
  this.unlockedImages = tierBenefits.images;
  this.monthlyAmount = tierBenefits.monthlyPrice;
  
  if (newTier === 0) {
    // Complete cancellation
    this.subscriptionStatus = 'expired';
    this.isActive = false;
    this.subscriptionEndDate = now;
  } else {
    // Start new billing period for downgraded tier
    const periodEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    this.currentPeriodStart = now;
    this.currentPeriodEnd = periodEnd;
    this.nextBillingDate = periodEnd;
    this.subscriptionStatus = 'active';
  }
  
  // Add to payment history
  this.paymentHistory.push({
    date: now,
    amount: 0, // No charge for downgrades/cancellations
    status: 'success',
    type: this.pendingTierChange.reason === 'cancellation' ? 'cancellation' : 'downgrade',
    fromTier: oldTier,
    toTier: newTier,
    periodStart: this.currentPeriodStart,
    periodEnd: this.currentPeriodEnd
  });
  
  // Clear pending change
  this.pendingTierChange = undefined;
  
  await this.save();
  return true;
};

// Method to renew subscription (called by scheduler)
UserMembershipSchema.methods.renewSubscription = async function() {
  if (this.subscriptionStatus !== 'active') {
    return false;
  }
  
  const now = new Date();
  const periodEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
  
  // Update billing period
  this.currentPeriodStart = now;
  this.currentPeriodEnd = periodEnd;
  this.nextBillingDate = periodEnd;
  this.lastPaymentDate = now;
  
  // Add to payment history (actual payment processing would be handled separately)
  this.paymentHistory.push({
    date: now,
    amount: this.monthlyAmount,
    status: 'pending', // Will be updated when payment processes
    type: 'subscription',
    toTier: this.tier,
    periodStart: now,
    periodEnd: periodEnd
  });
  
  await this.save();
  return true;
};

// Static method to get tier benefits
UserMembershipSchema.statics.getTierBenefits = function(tier) {
  const benefits = {
    0: { images: [], name: 'None', monthlyPrice: 0, oneTimePrice: 0 },
    1: { images: ['elf.png'], name: 'Forest Guardian', monthlyPrice: 650, oneTimePrice: 1950 },
    2: { images: ['elf.png', 'dwarf.png'], name: 'Mountain Warrior', monthlyPrice: 1350, oneTimePrice: 4050 },
    3: { images: ['elf.png', 'dwarf.png', 'mage.png'], name: 'Arcane Master', monthlyPrice: 2000, oneTimePrice: 6000 },
    4: { images: ['elf.png', 'dwarf.png', 'mage.png', 'dragon.png'], name: 'Dragon Lord', monthlyPrice: 4000, oneTimePrice: 12000 }
  };
  return benefits[tier] || benefits[0];
};

// Static method to get or create membership for user
UserMembershipSchema.statics.getOrCreateForUser = async function(userId) {
  let membership = await this.findOne({ user: userId });
  
  if (!membership) {
    membership = new this({
      user: userId,
      tier: 0,
      tierName: 'None',
      unlockedImages: [],
      subscriptionType: 'monthly',
      subscriptionStatus: 'pending',
      betaAccess: true // Default beta access
    });
    await membership.save();
  }
  
  return membership;
};

module.exports = mongoose.model('UserMembership', UserMembershipSchema); 