/**
 * MembershipManager - Unified membership system handler
 * Handles both purchase flows (index.html) and management flows (myprofile.html)
 * Manages subscriptions, avatar restrictions, and tier display across all pages
 */
class MembershipManager {
  constructor() {
    this.tiers = null;
    this.userMembership = null;
    this.isLoading = false;
    this.currentPage = this.detectCurrentPage();
    this.membershipTiers = {
      1: {
        name: 'Forest Guardian',
        description: 'Monthly subscription - Unlock the Elf profile image',
        images: ['elf.png'],
        price: 6.50,
        currency: 'CAD'
      },
      2: {
        name: 'Mountain Warrior',
        description: 'Monthly subscription - Unlock Elf and Dwarf profile images',
        images: ['elf.png', 'dwarf.png'],
        price: 13.50,
        currency: 'CAD'
      },
      3: {
        name: 'Arcane Master',
        description: 'Monthly subscription - Unlock Elf, Dwarf, and Mage profile images',
        images: ['elf.png', 'dwarf.png', 'mage.png'],
        price: 27,
        currency: 'CAD'
      },
      4: {
        name: 'Dragon Lord',
        description: 'Monthly subscription - Unlock all premium profile images',
        images: ['elf.png', 'dwarf.png', 'mage.png', 'dragon.png'],
        price: 54,
        currency: 'CAD'
      }
    };
  }

  /**
   * Detect which page we're on for context-aware functionality
   */
  detectCurrentPage() {
    const path = window.location.pathname;
    if (path.includes('myprofile.html')) return 'profile';
    if (path.includes('index.html') || path === '/' || path === '/views/') return 'index';
    return 'other';
  }

  /**
   * Initialize the membership system
   */
  async init() {
    console.log('🛡️ Initializing unified MembershipManager...', `Page: ${this.currentPage}`);
    
    if (this.isLoading) return;
    this.isLoading = true;

    try {
      // Load tiers and user membership status
      await Promise.all([
        this.loadTiers(),
        this.loadUserMembership()
      ]);

      // Set up event handlers
      this.setupEventHandlers();

      // Initialize based on current page context
      if (this.currentPage === 'index') {
        this.renderMembershipTiers();
      } else if (this.currentPage === 'profile') {
        this.updateHeroTierDisplay();
        this.setupAvatarRestrictions();
      }
      
      console.log('✅ Unified MembershipManager initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing MembershipManager:', error);
      this.showError('Failed to load membership information');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Set up event handlers for all pages
   */
  setupEventHandlers() {
    console.log('🎯 Setting up unified event handlers...');
    
    // Hero action button click (profile page)
    document.addEventListener('click', (e) => {
      if (e.target.closest('#hero-action-btn')) {
        console.log('🎯 Hero action button clicked!');
        e.preventDefault();
        this.handleHeroActionClick();
      }
    });
    
    console.log('✅ Event handlers setup complete');
  }

  /**
   * Handle hero action button clicks (profile page)
   */
  handleHeroActionClick() {
    console.log('🎯 Handling hero action click...');
    console.log('🔍 Current membership:', this.userMembership);
    
    if (this.userMembership?.isActive) {
      console.log('✅ User is active hero - showing manage membership modal');
      this.showManageMembershipModal();
    } else {
      console.log('❌ User is not active hero - redirecting to membership page');
      this.navigateToUpgrade();
    }
  }

  /**
   * Update the hero tier display section (profile page)
   */
  updateHeroTierDisplay() {
    const heroStatusCard = document.getElementById('hero-status-card');
    const heroTierName = document.getElementById('hero-tier-name');
    const heroTierDescription = document.getElementById('hero-tier-description');
    const heroTierLevel = document.getElementById('hero-tier-level');
    const heroActionBtn = document.getElementById('hero-action-btn');
    const heroActionText = document.getElementById('hero-action-text');
    const heroActionSubtitle = document.getElementById('hero-action-subtitle');
    const unlockedImagesGrid = document.getElementById('unlocked-images-grid');

    if (!heroStatusCard) {
      console.log('🚫 Hero tier elements not found on this page');
      return;
    }

    const membership = this.userMembership;

    if (membership && membership.isActive && membership.tier > 0) {
      // User is a hero
      const tierData = this.membershipTiers[membership.tier];
      
      heroStatusCard.classList.remove('no-hero');
      heroTierName.textContent = membership.tierName || tierData?.name || `Hero Tier ${membership.tier}`;
      heroTierDescription.textContent = membership.tier === 4 
        ? 'You have unlocked all premium profile images!' 
        : `You have access to ${membership.unlockedImages?.length || 0} premium profile images`;
      heroTierLevel.textContent = membership.tier;

      // Update action button for existing heroes
      heroActionBtn.className = 'epic-btn hero-action-btn change-plan';
      heroActionBtn.innerHTML = '<i class="fas fa-cog"></i><span id="hero-action-text">Manage Membership</span>';
      heroActionSubtitle.textContent = membership.tier === 4 
        ? 'You have the highest tier! Click to manage your membership.'
        : 'Click to upgrade your tier or manage your membership.';

    } else {
      // User is not a hero
      heroStatusCard.classList.add('no-hero');
      heroTierName.textContent = 'Not a Hero';
      heroTierDescription.textContent = 'Become a hero to unlock exclusive profile images and support the arena!';
      heroTierLevel.textContent = '0';

      // Update action button for non-heroes
      heroActionBtn.className = 'epic-btn hero-action-btn';
      heroActionBtn.innerHTML = '<i class="fas fa-crown"></i><span id="hero-action-text">Be a Hero</span>';
      heroActionSubtitle.textContent = 'Unlock exclusive profile images and support the arena!';
    }

    // Update unlocked images display
    this.updateUnlockedImagesDisplay(unlockedImagesGrid, membership);
    
    // Update avatar lock overlays when membership changes
    this.updateAvatarLockOverlays();
  }

  /**
   * Update the unlocked images display (profile page)
   */
  updateUnlockedImagesDisplay(container, membership) {
    if (!container) return;

    const allImages = ['elf.png', 'dwarf.png', 'mage.png', 'dragon.png'];
    const unlockedImages = membership?.unlockedImages || [];

    container.innerHTML = '';

    allImages.forEach((imageName) => {
      const isUnlocked = unlockedImages.includes(imageName);
      const imageItem = document.createElement('div');
      imageItem.className = `unlocked-image-item ${isUnlocked ? '' : 'locked'}`;
      
      const imagePath = `/assets/img/profiles/${imageName}`;
      const displayName = imageName.replace('.png', '').charAt(0).toUpperCase() + imageName.replace('.png', '').slice(1);
      
      imageItem.innerHTML = `
        <img src="${imagePath}" alt="${displayName}" class="unlocked-image">
        <span class="unlocked-image-name">${displayName}</span>
        ${isUnlocked ? 
          '<i class="fas fa-check-circle unlocked-icon"></i>' : 
          '<i class="fas fa-lock locked-icon"></i>'
        }
      `;
      
      container.appendChild(imageItem);
    });
  }

  /**
   * Set up avatar restrictions (profile page)
   */
  setupAvatarRestrictions() {
    console.log('🔒 Setting up avatar restrictions...');
    
    // Update avatar lock overlays based on membership
    this.updateAvatarLockOverlays();
    
    // Add click handlers to restricted avatar options (use capture phase to run first)
    document.addEventListener('click', (e) => {
      const avatarOption = e.target.closest('.avatar-option[data-tier]');
      if (avatarOption) {
        const requiredTier = parseInt(avatarOption.dataset.tier);
        const imageName = avatarOption.dataset.image;
        
        if (!this.isImageUnlocked(imageName)) {
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          this.showUpgradePrompt(requiredTier);
          return false;
        }
      }
    }, true); // Use capture phase to run before other handlers
  }

  /**
   * Update avatar lock overlays based on user's membership status
   */
  updateAvatarLockOverlays() {
    console.log('🔒 Updating avatar lock overlays...');
    
    const avatarOptions = document.querySelectorAll('.avatar-option[data-tier]');
    
    avatarOptions.forEach(option => {
      const requiredTier = parseInt(option.dataset.tier);
      const imageName = option.dataset.image;
      const lockOverlay = option.querySelector('.avatar-lock-overlay');
      
      if (lockOverlay) {
        const isUnlocked = this.isImageUnlocked(imageName);
        
        if (isUnlocked) {
          // Image is unlocked
          option.classList.remove('locked');
          lockOverlay.style.display = 'none';
          console.log(`✅ ${imageName} is unlocked`);
        } else {
          // Image is locked
          option.classList.add('locked');
          lockOverlay.style.display = 'flex';
          console.log(`🔒 ${imageName} is locked (requires tier ${requiredTier})`);
        }
      }
    });
  }

  /**
   * Check if a specific image is unlocked
   */
  isImageUnlocked(imageName) {
    const unlockedImages = this.userMembership?.unlockedImages || [];
    const isUnlocked = unlockedImages.includes(imageName);
    console.log(`🔍 Checking ${imageName}: unlocked=${isUnlocked}, userMembership:`, this.userMembership);
    return isUnlocked;
  }

  /**
   * Show upgrade prompt for locked images
   */
  showUpgradePrompt(requiredTier) {
    const tierNames = {
      1: 'Forest Guardian',
      2: 'Mountain Warrior', 
      3: 'Arcane Master',
      4: 'Dragon Lord'
    };

    const modal = document.createElement('div');
    modal.className = 'upgrade-prompt-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      backdrop-filter: blur(3px);
    `;
    
    modal.innerHTML = `
      <div style="
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        border: 2px solid rgba(255, 215, 0, 0.3);
        border-radius: 15px;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
      ">
        <div style="color: #ffd700; font-size: 3rem; margin-bottom: 1rem;">🔒</div>
        <h3 style="color: #ffd700; margin-bottom: 1rem;">Premium Content</h3>
        <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1.5rem;">
          This avatar requires <strong>${tierNames[requiredTier]}</strong> (Tier ${requiredTier}) or higher.
        </p>
        <div style="display: flex; gap: 1rem; justify-content: center;">
                     <button onclick="window.membershipManager.navigateToUpgrade(); this.closest('.upgrade-prompt-modal').remove();" style="
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
          ">
            Upgrade Now
          </button>
          <button onclick="this.closest('.upgrade-prompt-modal').remove();" style="
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
          ">
            Cancel
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);
  }

  /**
   * Navigate to upgrade options
   */
  navigateToUpgrade() {
    if (this.currentPage === 'profile') {
      // Navigate to index page and scroll to membership section
      window.location.href = '/views/index.html#membership-container';
    } else {
      // Already on index page, just scroll
      const membershipContainer = document.getElementById('membership-container');
      if (membershipContainer) {
        membershipContainer.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }

  /**
   * Show membership management modal for existing heroes
   */
  showManageMembershipModal() {
    console.log('🎯 Showing manage membership modal...');
    try {
      const membership = this.userMembership;
      if (!membership) {
        console.error('❌ No membership data available');
        return;
      }
      
      console.log('🔍 Membership data:', membership);
      const currentTier = membership.tier;
      const canUpgrade = currentTier < 4;
      console.log('📊 Current tier:', currentTier, 'Can upgrade:', canUpgrade);

    const modal = document.createElement('div');
    modal.className = 'modal membership-modal show active';
    modal.id = 'membership-management-modal';
    modal.setAttribute('data-visible', 'true');
    modal.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(0, 0, 0, 0.9) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      z-index: 999999 !important;
      backdrop-filter: blur(3px) !important;
      visibility: visible !important;
      opacity: 1 !important;
      overflow: auto !important;
    `;
    
    modal.innerHTML = `
      <div class="modal-content" style="
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        border: 2px solid rgba(255, 215, 0, 0.3);
        border-radius: 20px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        position: relative;
      ">
        <div class="modal-header" style="
          padding: 2rem;
          border-bottom: 1px solid rgba(255, 215, 0, 0.2);
          display: flex;
          justify-content: space-between;
          align-items: center;
        ">
          <h2 style="
            margin: 0;
            color: #ffd700;
            font-family: 'Cinzel', serif;
            font-size: 1.8rem;
          ">
            <i class="fas fa-cog"></i> Manage ${membership.tierName}
          </h2>
          <span class="close" style="
            color: rgba(255, 255, 255, 0.7);
            font-size: 2rem;
            cursor: pointer;
            line-height: 1;
            font-weight: bold;
          ">&times;</span>
        </div>
        <div class="modal-body" style="padding: 2rem;">
          <div style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; text-align: center;">
            <h3 style="color: #28a745; margin: 0 0 1rem 0; font-size: 1.3rem;">
              <i class="fas fa-crown"></i> Current: ${membership.tierName}
            </h3>
            <p style="margin: 0; color: rgba(255, 255, 255, 0.8);">
              You have ${membership.unlockedImages?.length || 0} unlocked profile images
            </p>
          </div>
          
          ${canUpgrade ? `
            <div style="margin-bottom: 2rem;">
              <h4 style="color: #ffd700; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-arrow-up"></i> Upgrade Your Tier
              </h4>
              <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1rem;">
                Unlock more premium profile images by upgrading to a higher tier.
              </p>
                             <button class="upgrade-button" style="
                 background: linear-gradient(45deg, #ffd700, #ffed4e);
                color: #000;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 25px;
                font-weight: 600;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 0.5rem;
              ">
                <i class="fas fa-crown"></i>
                View Upgrade Options
              </button>
            </div>
          ` : `
            <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; text-align: center;">
              <h4 style="color: #ffd700; margin: 0 0 0.5rem 0;">
                <i class="fas fa-dragon"></i> Dragon Lord - Maximum Tier!
              </h4>
              <p style="margin: 0; color: rgba(255, 255, 255, 0.8);">
                You have unlocked all available premium content. Thank you for your support!
              </p>
            </div>
            
            <div style="margin-bottom: 2rem;">
              <h4 style="color: #17a2b8; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-cog"></i> Subscription Management
              </h4>
              <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1rem;">
                Your monthly Dragon Lord subscription ($54 CAD/month) gives you access to all premium profile images.
              </p>
                             <button class="manage-payment-button" style="
                 background: rgba(23, 162, 184, 0.2);
                border: 1px solid rgba(23, 162, 184, 0.5);
                color: #17a2b8;
                padding: 0.75rem 1.5rem;
                border-radius: 25px;
                font-weight: 600;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 0.5rem;
              ">
                <i class="fas fa-credit-card"></i>
                Manage Payment & Upgrades
              </button>
            </div>
          `}
          
          <div style="border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 2rem; text-align: center;">
            <h4 style="color: #dc3545; margin-bottom: 1rem; display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
              <i class="fas fa-times-circle"></i> Cancel Membership
            </h4>
            <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 1.5rem; font-size: 0.9rem;">
              Canceling will stop your monthly subscription. You will retain access to premium images until the end of your current billing period.
            </p>
                           <button class="cancel-membership-button" style="
              background: rgba(220, 53, 69, 0.2);
              border: 1px solid rgba(220, 53, 69, 0.5);
              color: #dc3545;
              padding: 0.75rem 2rem;
              border-radius: 25px;
              font-weight: 600;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 0.5rem;
              margin: 0 auto;
            ">
              <i class="fas fa-times-circle"></i>
              Cancel Monthly Subscription
            </button>
          </div>
        </div>
      </div>
    `;

    console.log('✅ Modal HTML created, appending to body...');
    
    // Add modal-open class to body to prevent scroll
    document.body.classList.add('modal-open');
    
    document.body.appendChild(modal);
    console.log('✅ Modal appended to DOM successfully');
    
    // Debug: Check if modal is actually in DOM and visible
    const modalInDom = document.querySelector('.modal');
    console.log('🔍 Modal element in DOM:', modalInDom ? 'YES' : 'NO');
    if (modalInDom) {
      console.log('🔍 Modal computed styles:', {
        display: window.getComputedStyle(modalInDom).display,
        visibility: window.getComputedStyle(modalInDom).visibility,
        opacity: window.getComputedStyle(modalInDom).opacity,
        zIndex: window.getComputedStyle(modalInDom).zIndex,
        position: window.getComputedStyle(modalInDom).position
      });
      console.log('🔍 Modal getBoundingClientRect:', modalInDom.getBoundingClientRect());
    }

    // Setup button event listeners
    const upgradeButton = modal.querySelector('.upgrade-button');
    if (upgradeButton) {
      upgradeButton.addEventListener('click', () => {
        this.navigateToUpgrade();
        document.body.classList.remove('modal-open');
        modal.remove();
      });
    }
    
    const managePaymentButton = modal.querySelector('.manage-payment-button');
    if (managePaymentButton) {
      managePaymentButton.addEventListener('click', () => {
        this.navigateToUpgrade();
        document.body.classList.remove('modal-open');
        modal.remove();
      });
    }
    
    const cancelButton = modal.querySelector('.cancel-membership-button');
    if (cancelButton) {
      cancelButton.addEventListener('click', () => {
        this.cancelMembership(cancelButton);
      });
    }

    // Force modal visibility with timeout to override any conflicting styles
    setTimeout(() => {
      if (modal && modal.parentNode) {
        modal.style.display = 'flex';
        modal.style.visibility = 'visible';
        modal.style.opacity = '1';
        modal.style.zIndex = '999999';
        console.log('🔧 Force-applied modal visibility styles');
      }
    }, 100);

    // Close modal when clicking X or outside
    modal.addEventListener('click', (e) => {
      if (e.target === modal || e.target.classList.contains('close')) {
        document.body.classList.remove('modal-open');
        modal.remove();
      }
    });
    
    console.log('✅ Manage membership modal displayed successfully');
    } catch (error) {
      console.error('❌ Error creating manage membership modal:', error);
      this.showError('Failed to open membership management. Please refresh and try again.');
    }
  }

  /**
   * Cancel membership with confirmation
   */
  async cancelMembership(buttonElement) {
    const confirmed = confirm(
      `Are you sure you want to cancel your ${this.userMembership.tierName} subscription?\n\n` +
      'You will lose access to premium profile images at the end of your current billing period.\n\n' +
      'This action cannot be undone.'
    );

    if (!confirmed) {
      return;
    }

    const originalText = buttonElement.innerHTML;
    buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Canceling...';
    buttonElement.disabled = true;

    try {
      const response = await fetch('/api/membership/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          reason: 'User requested cancellation from profile page'
        })
      });

      const data = await response.json();

      if (data.success) {
        this.showSuccessMessage('Membership canceled successfully. You will retain access until the end of your billing period.');
        
        // Close modal and refresh membership status
        document.body.classList.remove('modal-open');
        buttonElement.closest('.modal').remove();
        await this.refreshMembershipStatus();
      } else {
        throw new Error(data.message || 'Failed to cancel membership');
      }
    } catch (error) {
      console.error('Error canceling membership:', error);
      this.showErrorMessage(error.message || 'Failed to cancel membership');
      
      // Restore button
      buttonElement.innerHTML = originalText;
      buttonElement.disabled = false;
    }
  }

  /**
   * Refresh membership status
   */
  async refreshMembershipStatus() {
    try {
      await this.loadUserMembership();
      
      if (this.currentPage === 'index') {
        this.renderMembershipTiers();
      } else if (this.currentPage === 'profile') {
        this.updateHeroTierDisplay();
      }
    } catch (error) {
      console.error('Error refreshing membership status:', error);
    }
  }

  /**
   * Load available membership tiers
   */
  async loadTiers() {
    try {
      const response = await fetch('/api/membership/tiers', {
      credentials: 'include'
    });
      const data = await response.json();
      
      if (data.success) {
        this.tiers = data.tiers;
        console.log('📋 Loaded membership tiers:', this.tiers);
      } else {
        throw new Error(data.message || 'Failed to load tiers');
      }
    } catch (error) {
      console.error('Error loading membership tiers:', error);
      throw error;
    }
  }

  /**
   * Load user's current membership status
   */
  async loadUserMembership() {
    try {
      // Check if user is logged in
      const userInfo = await this.getCurrentUser();
      if (!userInfo.isAuthenticated) {
        console.log('👤 User not authenticated, showing guest view');
        return;
      }

      console.log('🔄 Loading membership data from API...');
      const response = await fetch('/api/membership/status', {
        credentials: 'include'
      });
      const data = await response.json();
      
      console.log('📦 Membership API response:', data);
      
      if (data.success) {
        this.userMembership = data.membership;
        console.log('👑 User membership status loaded successfully:', this.userMembership);
        
      } else {
        console.warn('Failed to load user membership:', data.message);
        this.userMembership = null;
      }
    } catch (error) {
      console.error('Error loading user membership:', error);
      this.userMembership = null;
      // Don't throw - this is optional for guest users
    }
  }

  /**
   * Get current user info
   */
  async getCurrentUser() {
    try {
      const response = await fetch('/api/me', {
        credentials: 'include'
      });
      
      if (response.status === 401) {
        // User is not authenticated
        return { isAuthenticated: false };
      }
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const data = await response.json();
      return { ...data, isAuthenticated: true };
    } catch (error) {
      console.log('User authentication check failed:', error.message);
      return { isAuthenticated: false };
    }
  }

  /**
   * Render membership tiers
   */
  renderMembershipTiers() {
    const container = document.getElementById('membership-tiers');
    if (!container || !this.tiers) {
      console.warn('Cannot render tiers - missing container or tiers data');
      return;
    }

    console.log('🎨 Rendering membership tiers with user data:', this.userMembership);

    const tiersHtml = Object.entries(this.tiers).map(([tierNum, tier]) => {
      const currentUserTier = this.userMembership?.isActive ? this.userMembership.tier : 0;
      const tierNumber = parseInt(tierNum);
      const isCurrentTier = this.userMembership?.isActive && currentUserTier === tierNumber;
      const isLowerTier = this.userMembership?.isActive && currentUserTier > tierNumber;
      const isFeatured = tierNum == '3'; // Arcane Master is featured
      
      console.log(`🔍 Tier ${tierNum} check:`, {
        tierNumber,
        currentUserTier,
        isCurrentTier,
        isLowerTier,
        userIsActive: this.userMembership?.isActive
      });
      
      let buttonText, buttonClass, buttonDisabled, buttonAction;
      
      if (isCurrentTier) {
        buttonText = `<i class="fas fa-check-circle"></i> Current Tier`;
        buttonClass = 'tier-btn current';
        buttonDisabled = 'disabled';
        buttonAction = '';
      } else if (isLowerTier) {
        buttonText = `<i class="fas fa-check"></i> Unlocked`;
        buttonClass = 'tier-btn unlocked';
        buttonDisabled = 'disabled';
        buttonAction = '';
      } else {
        buttonText = `<i class="fas fa-credit-card"></i> $${tier.providers.square.price} CAD/month`;
        buttonClass = 'tier-btn monthly';
        buttonDisabled = '';
        buttonAction = `onclick="window.membershipManager.subscribeTier(${tierNum}, 'square', 'monthly', this)"`;
      }
      
      return `
        <div class="membership-tier ${isFeatured ? 'featured' : ''} ${isCurrentTier ? 'current-tier' : ''} ${isLowerTier ? 'unlocked-tier' : ''}">
          ${isCurrentTier ? '<div class="current-tier-badge">Current Tier</div>' : ''}
          ${isLowerTier ? '<div class="unlocked-tier-badge">Unlocked</div>' : ''}
          
          <div class="tier-name">${tier.name}</div>
          
          <div class="tier-price">
            <span class="currency">$</span>${tier.providers.square.price}<span style="font-size: 1rem; color: rgba(255,255,255,0.7);">/month</span>
          </div>
          
          <div class="tier-description">
            ${tier.description || this.getTierDescription(tierNum)}
          </div>
          
          <div class="tier-images">
            ${this.renderTierImages(tier.images || this.getTierImages(tierNum))}
          </div>
          
          <div class="tier-buttons">
            <button class="${buttonClass}" ${buttonDisabled} ${buttonAction}>
              ${buttonText}
            </button>
          </div>
        </div>
      `;
    }).join('');

    container.innerHTML = tiersHtml;

    // Show user membership status if logged in
    this.renderMembershipStatus();
  }

  /**
   * Get description for tier
   */
  getTierDescription(tierNum) {
    const descriptions = {
      '1': 'Unlock the graceful Elf profile image',
      '2': 'Unlock Elf and sturdy Dwarf profile images', 
      '3': 'Unlock Elf, Dwarf, and mystical Mage profile images',
      '4': 'Unlock all profile images including the legendary Dragon'
    };
    return descriptions[tierNum] || 'Unknown tier';
  }

  /**
   * Get images for tier
   */
  getTierImages(tierNum) {
    const tierImages = {
      '1': ['elf.png'],
      '2': ['elf.png', 'dwarf.png'],
      '3': ['elf.png', 'dwarf.png', 'mage.png'],
      '4': ['elf.png', 'dwarf.png', 'mage.png', 'dragon.png']
    };
    return tierImages[tierNum] || [];
  }

  /**
   * Render tier images
   */
  renderTierImages(images) {
    const imageNames = {
      'elf.png': 'Elf',
      'dwarf.png': 'Dwarf', 
      'mage.png': 'Mage',
      'dragon.png': 'Dragon'
    };

    return images.map(img => 
      `<img src="/assets/img/profiles/${img}" alt="${imageNames[img]}" class="tier-image" title="${imageNames[img]}">`
    ).join('');
  }

  /**
   * Render membership status for logged in users
   */
  renderMembershipStatus() {
    console.log('🎨 renderMembershipStatus called');
    const container = document.getElementById('membership-container');
    if (!container) {
      console.warn('🚫 Membership container not found');
      return;
    }

    console.log('📊 Current membership data:', this.userMembership);

    // Remove any existing status display
    const existingStatus = container.querySelector('.membership-status');
    if (existingStatus) {
      console.log('🧹 Removing existing membership status display');
      existingStatus.remove();
    }

    if (this.userMembership && this.userMembership.isActive && this.userMembership.tier > 0) {
      console.log('✅ User qualifies for membership status display - rendering...');
      const statusHtml = `
        <div class="membership-status" style="
          background: rgba(0, 255, 0, 0.1);
          border: 2px solid rgba(0, 255, 0, 0.3);
          border-radius: 15px;
          padding: 2rem;
          margin-bottom: 2rem;
          text-align: center;
          backdrop-filter: blur(10px);
        ">
          <h3 style="color: #00ff00; margin-bottom: 1rem; font-family: 'Cinzel', serif; font-size: 1.8rem;">
            <i class="fas fa-crown"></i> ${this.userMembership.tierName} Active!
          </h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
            <div style="background: rgba(255, 255, 255, 0.1); padding: 1rem; border-radius: 10px;">
              <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">Current Tier</p>
              <p style="margin: 0; color: #ffd700; font-weight: bold; font-size: 1.1rem;">${this.userMembership.tierName}</p>
            </div>
            <div style="background: rgba(255, 255, 255, 0.1); padding: 1rem; border-radius: 10px;">
              <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">Status</p>
              <p style="margin: 0; color: #00ff00; font-weight: bold; font-size: 1.1rem;">${this.userMembership.subscriptionStatus || 'Active'}</p>
            </div>
            ${this.userMembership.nextBillingDate ? `
            <div style="background: rgba(255, 255, 255, 0.1); padding: 1rem; border-radius: 10px;">
              <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">Next Billing</p>
              <p style="margin: 0; color: #ffd700; font-weight: bold; font-size: 1.1rem;">${new Date(this.userMembership.nextBillingDate).toLocaleDateString()}</p>
            </div>
            ` : ''}
            <div style="background: rgba(255, 255, 255, 0.1); padding: 1rem; border-radius: 10px;">
              <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">Unlocked Images</p>
              <p style="margin: 0; color: #ffd700; font-weight: bold; font-size: 1.1rem;">${this.userMembership.unlockedImages?.length || 0}</p>
            </div>
          </div>
          
          <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-top: 1.5rem;">
            ${this.userMembership.tier < 4 ? `
            <button class="epic-btn" onclick="window.location.hash = 'membership-tiers'; document.getElementById('membership-tiers').scrollIntoView({behavior: 'smooth'});" style="background: rgba(255, 193, 7, 0.2); border-color: rgba(255, 193, 7, 0.5);">
              <i class="fas fa-arrow-up"></i>
              Upgrade Tier
            </button>
            ` : `
            <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 10px; padding: 1rem; margin-bottom: 1rem;">
              <p style="margin: 0; color: #ffd700; font-weight: bold;">
                <i class="fas fa-dragon"></i> Maximum Tier Achieved!
              </p>
              <p style="margin: 0.5rem 0 0 0; color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">
                You have unlocked all available premium content. Thank you for your support!
              </p>
            </div>
            `}
            
            <button class="epic-btn" onclick="window.membershipManager.manageSubscription()" style="background: rgba(23, 162, 184, 0.2); border-color: rgba(23, 162, 184, 0.5);">
              <i class="fas fa-cog"></i>
              Manage Subscription
            </button>
            
            ${this.userMembership.subscriptionStatus !== 'cancelled' ? `
            <button class="tier-btn" onclick="window.membershipManager.cancelSubscription()" style="background: rgba(255, 0, 0, 0.2); border-color: rgba(255, 0, 0, 0.5); color: #ff6b6b;">
              <i class="fas fa-times"></i> Cancel Subscription
            </button>
            ` : `
            <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 10px; padding: 1rem; text-align: center;">
              <p style="margin: 0; color: #ffd700; font-weight: bold;">
                <i class="fas fa-info-circle"></i> Subscription Cancelled
              </p>
              <p style="margin: 0.5rem 0 0 0; color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">
                You will retain access until your current period ends.
              </p>
            </div>
            `}
          </div>
        </div>
      `;
      
             // Insert status at the beginning of the membership container
       const membershipInfo = container.querySelector('.membership-info');
       if (membershipInfo) {
         console.log('📍 Inserting status before membership-info');
         membershipInfo.insertAdjacentHTML('beforebegin', statusHtml);
       } else {
         console.log('📍 Inserting status at beginning of container (fallback)');
         // Fallback: insert at beginning of container
         container.insertAdjacentHTML('afterbegin', statusHtml);
       }
       console.log('✅ Membership status display rendered successfully');
     } else {
       console.log('❌ User does not qualify for membership status display');
       console.log('   - Has membership data:', !!this.userMembership);
       console.log('   - Is active:', this.userMembership?.isActive);
       console.log('   - Tier:', this.userMembership?.tier);
     }
   }

  /**
   * Subscribe to a tier
   */
  async subscribeTier(tier, provider = 'square', subscriptionType = 'monthly', buttonElement = null) {
    console.log(`🛡️ Purchasing tier ${tier} with ${provider} (${subscriptionType})`);

    // Check if user is logged in
    const userInfo = await this.getCurrentUser();
    if (!userInfo.isAuthenticated) {
      alert('Please log in to purchase a membership tier');
      return;
    }

    try {
      // Show loading state - improved button detection
      let button = buttonElement;
      
      // Try to get button from the event if not provided
      if (!button && typeof window !== 'undefined' && window.event && window.event.target) {
        button = window.event.target;
        // If it's an icon inside a button, get the button
        if (button.tagName === 'I' && button.parentElement.tagName === 'BUTTON') {
          button = button.parentElement;
        }
      }
      
      let originalText = '';
      if (button) {
        originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        button.disabled = true;
      }

      // Determine if this is an upgrade
      const currentTier = this.userMembership?.tier || 0;
      const isUpgrade = currentTier > 0 && tier > currentTier;
      const changeType = isUpgrade ? 'upgrade' : 'new';

      console.log(`💰 Payment type: ${changeType} (current: ${currentTier}, new: ${tier})`);

      // Create payment
      const response = await fetch('/api/membership/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          tier,
          provider,
          subscriptionType,
          changeType
        })
      });

      const data = await response.json();

      if (data.success) {
        if (provider === 'square') {
          // Add changeType to the order object for payment processing
          data.result.changeType = data.changeType;
          // Handle Square payment with Web Payments SDK
          await this.handleSquarePayment(data.result, tier);
        } else if (provider === 'coinbase') {
          // Redirect to Coinbase payment page
          if (data.result.hosted_url) {
            console.log('🔗 Redirecting to Coinbase payment:', data.result.hosted_url);
            window.open(data.result.hosted_url, '_blank');
            this.showSuccess(`Payment created successfully! Complete your payment in the new tab.`);
          } else {
            throw new Error('No payment URL received from Coinbase');
          }
        } else {
          throw new Error('Unsupported payment provider');
        }
      } else {
        throw new Error(data.message || 'Failed to create payment');
      }
    } catch (error) {
      console.error('Error subscribing to tier:', error);
      this.showError(error.message || 'Failed to create subscription');
    } finally {
      // Restore button state
      if (typeof button !== 'undefined' && button && originalText) {
        button.innerHTML = originalText;
        button.disabled = false;
      }
    }
  }

  /**
   * Cancel/reset membership
   */
  async cancelSubscription() {
    // Check if already cancelled
    if (this.userMembership && this.userMembership.subscriptionStatus === 'cancelled') {
      this.showEnhancedCancellationModal('Your subscription is already cancelled. You will retain access until the end of your current period.');
      return;
    }

    if (!confirm('Are you sure you want to cancel your monthly subscription? You will lose access to premium profile images at the end of your current billing period.')) {
      return;
    }

    try {
      const response = await fetch('/api/membership/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          reason: 'User requested cancellation via blacksmith page'
        })
      });

      const data = await response.json();

      if (data.success) {
        // Show enhanced modal for cancellation
        this.showEnhancedCancellationModal(data.message);
        // Reload membership status
        await this.loadUserMembership();
        this.renderMembershipTiers();
      } else {
        throw new Error(data.message || 'Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      this.showError(error.message || 'Failed to cancel subscription');
    }
  }

  /**
   * Show subscription management options
   */
  manageSubscription() {
    const membership = this.userMembership;
    if (!membership) {
      this.showError('No membership data available');
      return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    `;
    
    modal.innerHTML = `
      <div style="
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        border: 2px solid rgba(255, 215, 0, 0.3);
        border-radius: 20px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
      ">
        <div style="
          padding: 2rem;
          border-bottom: 1px solid rgba(255, 215, 0, 0.2);
          display: flex;
          justify-content: space-between;
          align-items: center;
        ">
          <h2 style="
            margin: 0;
            color: #ffd700;
            font-family: 'Cinzel', serif;
            font-size: 1.8rem;
          ">
            <i class="fas fa-cog"></i> Manage ${membership.tierName}
          </h2>
          <button onclick="this.closest('.modal').remove()" style="
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
          ">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div style="padding: 2rem;">
          <div style="
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
          ">
            <h3 style="color: #28a745; margin: 0 0 1rem 0; font-size: 1.3rem;">
              <i class="fas fa-crown"></i> ${membership.tierName} - Active
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
              <div>
                <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">Status</p>
                <p style="margin: 0; color: #00ff00; font-weight: bold;">${membership.subscriptionStatus || 'Active'}</p>
              </div>
              <div>
                <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">Unlocked Images</p>
                <p style="margin: 0; color: #ffd700; font-weight: bold;">${membership.unlockedImages?.length || 0}/4</p>
              </div>
              ${membership.nextBillingDate ? `
              <div>
                <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 0.9rem;">Next Billing</p>
                <p style="margin: 0; color: #ffd700; font-weight: bold;">${new Date(membership.nextBillingDate).toLocaleDateString()}</p>
              </div>
              ` : ''}
            </div>
          </div>
          
          ${membership.tier < 4 ? `
          <div style="margin-bottom: 2rem;">
            <h4 style="color: #ffd700; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-arrow-up"></i> Upgrade Your Tier
            </h4>
            <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1rem;">
              Unlock more premium profile images by upgrading to a higher tier.
            </p>
            <button onclick="this.closest('.modal').remove(); window.location.hash = 'membership-tiers'; document.getElementById('membership-tiers').scrollIntoView({behavior: 'smooth'});" style="
              background: linear-gradient(45deg, #ffd700, #ffed4e);
              color: #000;
              border: none;
              padding: 0.75rem 1.5rem;
              border-radius: 25px;
              font-weight: 600;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 0.5rem;
            ">
              <i class="fas fa-crown"></i>
              View Upgrade Options
            </button>
          </div>
          ` : `
          <div style="
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
          ">
            <h4 style="color: #ffd700; margin: 0 0 0.5rem 0;">
              <i class="fas fa-dragon"></i> Maximum Tier Achieved!
            </h4>
            <p style="margin: 0; color: rgba(255, 255, 255, 0.8);">
              You have unlocked all available premium content. Thank you for your support!
            </p>
          </div>
          `}
          
          <div style="margin-bottom: 2rem;">
            <h4 style="color: #17a2b8; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-credit-card"></i> Payment Management
            </h4>
            <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1rem;">
              Your monthly ${membership.tierName} subscription is processed securely through ${membership.paymentProvider || 'Square'}.
            </p>
            <button onclick="alert('Payment method updates will be available in a future update. For now, please contact support if you need to update your payment method.');" style="
              background: rgba(23, 162, 184, 0.2);
              border: 1px solid rgba(23, 162, 184, 0.5);
              color: #17a2b8;
              padding: 0.75rem 1.5rem;
              border-radius: 25px;
              font-weight: 600;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 0.5rem;
            ">
              <i class="fas fa-credit-card"></i>
              Update Payment Method
            </button>
          </div>
          
          ${membership.subscriptionStatus !== 'cancelled' ? `
          <div style="
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 2rem;
            text-align: center;
          ">
            <h4 style="color: #dc3545; margin-bottom: 1rem; display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
              <i class="fas fa-times-circle"></i> Cancel Subscription
            </h4>
            <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 1.5rem; font-size: 0.9rem;">
              Canceling will stop your monthly subscription. You will retain access to premium images until the end of your current billing period.
            </p>
            <button onclick="this.closest('.modal').remove(); membershipManager.cancelSubscription();" style="
              background: rgba(220, 53, 69, 0.2);
              border: 1px solid rgba(220, 53, 69, 0.5);
              color: #dc3545;
              padding: 0.75rem 2rem;
              border-radius: 25px;
              font-weight: 600;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 0.5rem;
              margin: 0 auto;
            ">
              <i class="fas fa-times-circle"></i>
              Cancel Monthly Subscription
            </button>
          </div>
          ` : `
          <div style="
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 2rem;
            text-align: center;
          ">
            <h4 style="color: #ffd700; margin-bottom: 1rem; display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
              <i class="fas fa-info-circle"></i> Subscription Cancelled
            </h4>
            <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 1.5rem; font-size: 0.9rem;">
              Your subscription has been cancelled. You will retain access to premium features until the end of your current billing period.
            </p>
            <div style="
              background: rgba(255, 193, 7, 0.1);
              border: 1px solid rgba(255, 193, 7, 0.3);
              border-radius: 10px;
              padding: 1rem;
              margin: 0 auto;
              max-width: 300px;
            ">
              <p style="margin: 0; color: #ffd700; font-weight: bold; font-size: 0.9rem;">
                Access expires: ${membership.currentPeriodEnd ? new Date(membership.currentPeriodEnd).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>
          `}
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  /**
   * Handle payment success callback
   */
  async handlePaymentSuccess(paymentId, provider) {
    console.log('🎉 Payment success callback:', { paymentId, provider });
    
    try {
      // Reload membership status
      await this.loadUserMembership();
      this.renderMembershipTiers();
      
      // Show success message with tier information
      const tierName = this.userMembership?.tierName || 'Hero';
      const tierNumber = this.userMembership?.tier || 0;
      const unlockedImages = this.userMembership?.unlockedImages?.length || 0;
      
      this.showSuccess(
        `🎉 Welcome to ${tierName}! 🎉\n\n` +
        `Your Hero Tier ${tierNumber} membership is now active!\n` +
        `You have unlocked ${unlockedImages} premium profile image${unlockedImages !== 1 ? 's' : ''}.\n\n` +
        `Visit your profile page to change your avatar and manage your membership.\n\n` +
        `The page will refresh automatically when you close this confirmation.`,
        true // Enhanced version
      );
    } catch (error) {
      console.error('Error handling payment success:', error);
      this.showSuccess('Membership activated successfully! Please refresh the page to see your new status.');
    }
  }

  /**
   * Show success message
   */
  showSuccess(message, enhanced = false) {
    console.log('✅ Success:', message);
    
    if (enhanced) {
      // Enhanced success modal for major events like subscription activation
      this.showEnhancedSuccessModal(message);
    } else {
      // Regular toast notification
      this.showToast(message, 'success');
    }
  }

  /**
   * Show enhanced success modal for important events (persistent until manually closed)
   */
  showEnhancedSuccessModal(message) {
    const modal = document.createElement('div');
    modal.className = 'membership-success-modal';
    modal.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(0, 0, 0, 0.9) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      z-index: 999999 !important;
      backdrop-filter: blur(5px) !important;
    `;

    modal.innerHTML = `
      <div style="
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        border: 3px solid #ffd700;
        border-radius: 20px;
        padding: 3rem;
        max-width: 500px;
        width: 90%;
        text-align: center;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
        animation: celebrationBounce 0.6s ease;
        position: relative;
      ">
        <div style="
          font-size: 4rem;
          margin-bottom: 1rem;
          color: #ffd700;
          animation: celebrationGlow 2s ease infinite;
        ">
          👑
        </div>
        <h2 style="
          color: #ffd700;
          font-family: 'Cinzel', serif;
          font-size: 2.2rem;
          margin-bottom: 1.5rem;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        ">
          Membership Activated!
        </h2>
        <p style="
          color: rgba(255, 255, 255, 0.9);
          font-size: 1.1rem;
          line-height: 1.6;
          margin-bottom: 2rem;
          white-space: pre-line;
        ">${message}</p>
        <div style="margin-bottom: 1rem;">
          <p style="
            color: rgba(255, 215, 0, 0.8);
            font-size: 0.9rem;
            font-style: italic;
          ">This confirmation will stay open until you close it manually</p>
        </div>
        <button class="close-modal" style="
          background: linear-gradient(45deg, #ffd700, #ffed4e);
          color: #000;
          border: none;
          padding: 1rem 2rem;
          border-radius: 25px;
          font-weight: 600;
          font-size: 1.1rem;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          margin-right: 1rem;
        ">
          <i class="fas fa-check"></i>
          Awesome!
        </button>
        <button class="close-modal" style="
          background: rgba(255, 255, 255, 0.1);
          color: #fff;
          border: 1px solid rgba(255, 255, 255, 0.3);
          padding: 1rem 1.5rem;
          border-radius: 25px;
          font-weight: 600;
          font-size: 1rem;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
        ">
          <i class="fas fa-times"></i>
          Close
        </button>
      </div>
    `;

    // Add celebration animations if not already present
    if (!document.getElementById('celebration-styles')) {
      const style = document.createElement('style');
      style.id = 'celebration-styles';
      style.textContent = `
        @keyframes celebrationBounce {
          0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
          50% { transform: scale(1.05) rotate(2deg); }
          70% { transform: scale(0.95) rotate(-1deg); }
          100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }
        @keyframes celebrationGlow {
          0%, 100% { text-shadow: 0 0 20px #ffd700, 0 0 40px #ffd700; }
          50% { text-shadow: 0 0 30px #ffd700, 0 0 60px #ffd700, 0 0 80px #ffd700; }
        }
      `;
      document.head.appendChild(style);
    }

    // Add close event listeners
    modal.addEventListener('click', (e) => {
      if (e.target === modal || e.target.classList.contains('close-modal')) {
        modal.remove();
        // Reload page when user manually closes the success modal
        window.location.reload();
      }
    });

    document.body.appendChild(modal);
    
    // Prevent accidental closure and ensure visibility
    setTimeout(() => {
      if (modal && modal.parentNode) {
        modal.style.display = 'flex';
        modal.style.opacity = '1';
        modal.style.visibility = 'visible';
      }
    }, 100);
  }

  /**
   * Show toast notification
   */
  showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 1rem 1.5rem;
      border-radius: 10px;
      color: white;
      font-weight: 600;
      z-index: 10001;
      max-width: 400px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(10px);
      animation: slideInRight 0.3s ease;
      ${type === 'success' ? 
        'background: rgba(40, 167, 69, 0.9); border: 1px solid rgba(40, 167, 69, 0.3);' : 
        'background: rgba(220, 53, 69, 0.9); border: 1px solid rgba(220, 53, 69, 0.3);'}
    `;

    toast.innerHTML = `
      <div style="display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'}"></i>
        <span>${message}</span>
      </div>
    `;

    // Add animation styles if not already present
    if (!document.getElementById('toast-styles')) {
      const style = document.createElement('style');
      style.id = 'toast-styles';
      style.textContent = `
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(toast);

    // Auto remove after 4 seconds
    setTimeout(() => {
      toast.style.animation = 'slideOutRight 0.3s ease';
      setTimeout(() => {
        if (toast.parentNode) {
          toast.remove();
        }
      }, 300);
    }, 4000);
  }

  /**
   * Show error message
   */
  showError(message) {
    console.error('❌ Error:', message);
    this.showToast(message, 'error');
  }

  /**
   * Show success message (alias for consistency)
   */
  showSuccessMessage(message) {
    this.showToast(message, 'success');
  }

  /**
   * Show error message (alias for consistency)
   */
  showErrorMessage(message) {
    this.showToast(message, 'error');
  }

  /**
   * Handle Square payment with Web Payments SDK
   */
  async handleSquarePayment(order, tier) {
    try {
      // Check if Square Web Payments SDK is loaded
      if (typeof Square === 'undefined') {
        // Load Square Web Payments SDK dynamically
        await this.loadSquareSDK();
      }

      const applicationId = 'sandbox-sq0idb-9JEx9IQ7SzKcTwx3nZPEyA'; // Your sandbox app ID
      const locationId = order.location_id; // Use the location ID from the order

      // Initialize Square payments
      const payments = Square.payments(applicationId, locationId);
      
      // Create payment form
      this.showSquarePaymentForm(payments, order, tier);
      
    } catch (error) {
      console.error('Error handling Square payment:', error);
      this.showError('Failed to initialize Square payment');
    }
  }

  /**
   * Load Square Web Payments SDK
   */
  async loadSquareSDK() {
    return new Promise((resolve, reject) => {
      if (typeof Square !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://sandbox.web.squarecdn.com/v1/square.js';
      script.onload = () => {
        console.log('✅ Square SDK loaded successfully');
        resolve();
      };
      script.onerror = (error) => {
        console.error('❌ Failed to load Square SDK:', error);
        reject(error);
      };
      document.head.appendChild(script);
    });
  }

  /**
   * Show Square payment form
   */
  async showSquarePaymentForm(payments, order, tier) {
    try {
      // Show payment modal first, then attach card
      await this.showPaymentModal(order, async () => {
        console.log('🔧 Initializing Square card form...');
        
        // Initialize digital wallets first
        await this.initializeDigitalWallets(payments, order, tier);
        
        // Create and attach card payment method after modal is shown
        const card = await payments.card({
          style: {
            '.input-container': {
              borderColor: '#cccccc',
              borderRadius: '5px'
            },
            '.input-container.is-focus': {
              borderColor: '#007cba'
            },
            '.input-container.is-error': {
              borderColor: '#ff1600'
            }
          }
        });
        await card.attach('#square-card-container');
        
        console.log('✅ Square card attached successfully');
        
        // Return the payment handler function
        return async () => {
          console.log('💳 Starting payment tokenization...');
          const tokenResult = await card.tokenize();
          
          if (tokenResult.status === 'OK') {
            console.log('✅ Card tokenization successful');
            
            // Process payment with backend
            const response = await fetch('/api/membership/process-payment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              credentials: 'include',
              body: JSON.stringify({
                orderId: order.id,
                sourceId: tokenResult.token,
                verificationToken: tokenResult.details?.verification_token,
                tier: tier,
                changeType: order.changeType || 'new'
              })
            });

            const data = await response.json();
            
            if (data.success) {
              // Call the enhanced success handler
              await this.handlePaymentSuccess(data.paymentId, 'square');
              // Note: handlePaymentSuccess already reloads membership status and renders tiers
            } else {
              throw new Error(data.message || 'Payment failed');
            }
          } else {
            console.error('❌ Card tokenization failed:', tokenResult.errors);
            throw new Error(`Card tokenization failed: ${tokenResult.errors?.[0]?.detail || 'Unknown error'}`);
          }
        };
      });
      
    } catch (error) {
      console.error('Error showing Square payment form:', error);
      this.showError('Failed to initialize payment form');
    }
  }

  /**
   * Show payment modal (basic implementation)
   */
  async showPaymentModal(order, handlePaymentSetup) {
    console.log('🏪 Order object received:', order);
    return new Promise(async (resolve, reject) => {
      // Create a simple modal for payment
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      `;

      const content = document.createElement('div');
      content.style.cssText = `
        background: white;
        padding: 2rem;
        border-radius: 10px;
        max-width: 400px;
        width: 90%;
      `;

      // Calculate amount from order data
      let amount = 0;
      if (order.totalMoney && order.totalMoney.amount) {
        amount = order.totalMoney.amount / 100;
      } else if (order.total_money && order.total_money.amount) {
        amount = order.total_money.amount / 100;
      } else if (order.lineItems && order.lineItems.length > 0) {
        // Calculate from line items
        amount = order.lineItems.reduce((total, item) => {
          const itemAmount = item.basePriceMoney?.amount || item.base_price_money?.amount || 0;
          const quantity = parseInt(item.quantity) || 1;
          return total + (itemAmount * quantity);
        }, 0) / 100;
      } else if (order.line_items && order.line_items.length > 0) {
        // Calculate from line items (snake_case)
        amount = order.line_items.reduce((total, item) => {
          const itemAmount = item.base_price_money?.amount || 0;
          const quantity = parseInt(item.quantity) || 1;
          return total + (itemAmount * quantity);
        }, 0) / 100;
      }

      content.innerHTML = `
        <h3>Complete Your Purchase</h3>
        <p>Amount: $${amount.toFixed(2)} CAD</p>
        <div style="background: rgba(0, 123, 186, 0.1); border: 1px solid rgba(0, 123, 186, 0.3); border-radius: 5px; padding: 0.75rem; margin: 1rem 0; font-size: 0.9rem;">
          <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
            <i class="fas fa-credit-card" style="color: #007cba;"></i>
            <strong>Accepted Payment Methods</strong>
          </div>
          <div style="color: #666;">
            • Credit/Debit: Visa, Mastercard, Amex, Discover, JCB, UnionPay<br>
            • Digital Wallets: Apple Pay, Google Pay, Cash App Pay<br>
            • Buy Now Pay Later: Afterpay/Clearpay
          </div>
        </div>
        
        <!-- Digital Wallet Buttons -->
        <div style="display: flex; gap: 1rem; margin: 1rem 0; justify-content: center; flex-wrap: wrap;">
          <div id="apple-pay-button" style="display: none; width: 150px; height: 40px; cursor: pointer; border-radius: 4px;"></div>
          <div id="google-pay-button" style="display: none; width: 150px; height: 40px; cursor: pointer; border-radius: 4px;"></div>
          <div id="afterpay-button" style="display: none; width: 150px; height: 40px; cursor: pointer; border-radius: 4px;"></div>
        </div>
        
        <div style="text-align: center; margin: 1rem 0; color: #666; font-size: 0.9rem;">
          <div style="display: flex; align-items: center; gap: 0.5rem; justify-content: center;">
            <hr style="flex: 1; border: none; border-top: 1px solid #ddd;">
            <span>or pay with card</span>
            <hr style="flex: 1; border: none; border-top: 1px solid #ddd;">
          </div>
        </div>
        
        <div id="square-card-container" style="margin: 1rem 0; min-height: 100px;"></div>
        <button id="pay-button" style="margin-top: 1rem; padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;" disabled>Initializing...</button>
        <button id="cancel-button" style="margin-top: 1rem; margin-left: 10px; padding: 10px 20px; background: #ccc; color: black; border: none; border-radius: 5px; cursor: pointer;">Cancel</button>
      `;

      modal.appendChild(content);
      document.body.appendChild(modal);

      try {
        // Initialize payment after modal is in DOM
        const paymentHandler = await handlePaymentSetup();
        
        // Enable pay button
        const payButton = document.getElementById('pay-button');
        payButton.disabled = false;
        payButton.textContent = 'Pay Now';

        // Handle pay button
        payButton.onclick = async () => {
          try {
            payButton.disabled = true;
            payButton.textContent = 'Processing...';
            await paymentHandler(); // Call the function
            modal.remove();
            resolve();
          } catch (error) {
            console.error('Payment error:', error);
            this.showError(error.message);
            payButton.disabled = false;
            payButton.textContent = 'Pay Now';
          }
        };

        // Handle cancel button
        document.getElementById('cancel-button').onclick = () => {
          modal.remove();
          resolve();
        };

      } catch (error) {
        console.error('Error setting up payment:', error);
        this.showError('Failed to initialize payment form');
        modal.remove();
        reject(error);
      }
    });
  }

  /**
   * Show enhanced cancellation modal for important events (persistent until manually closed)
   */
  showEnhancedCancellationModal(message) {
    const modal = document.createElement('div');
    modal.className = 'membership-cancellation-modal';
    modal.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(0, 0, 0, 0.9) !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      z-index: 999999 !important;
      backdrop-filter: blur(5px) !important;
    `;

    modal.innerHTML = `
      <div style="
        background: linear-gradient(135deg, #1a1a2e, #16213e);
        border: 3px solid #ffd700;
        border-radius: 20px;
        padding: 3rem;
        max-width: 500px;
        width: 90%;
        text-align: center;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
        animation: cancellationBounce 0.6s ease;
      ">
        <div style="
          position: absolute;
          top: 15px;
          right: 20px;
          font-size: 1.5rem;
          color: #ccc;
          cursor: pointer;
          hover: color: #fff;
        " class="close-modal" title="Close">
          ×
        </div>
        <div style="
          font-size: 4rem;
          margin-bottom: 1rem;
          color: #ffd700;
          animation: cancellationGlow 2s ease infinite;
        ">
          👑
        </div>
        <h2 style="
          color: #ffd700;
          font-family: 'Cinzel', serif;
          font-size: 2.2rem;
          margin-bottom: 1.5rem;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        ">
          Subscription Cancelled
        </h2>
        <p style="
          color: rgba(255, 255, 255, 0.9);
          font-size: 1.1rem;
          line-height: 1.6;
          margin-bottom: 2rem;
          white-space: pre-line;
        ">${message}</p>
        <div style="margin-bottom: 1rem;">
          <p style="
            color: rgba(255, 215, 0, 0.8);
            font-size: 0.9rem;
            font-style: italic;
          ">This confirmation will stay open until you close it manually</p>
        </div>
        <button class="close-modal" style="
          background: linear-gradient(45deg, #ffd700, #ffed4e);
          color: #000;
          border: none;
          padding: 1rem 2rem;
          border-radius: 25px;
          font-weight: 600;
          font-size: 1.1rem;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
          margin-right: 1rem;
        ">
          <i class="fas fa-check"></i>
          Okay
        </button>
        <button class="close-modal" style="
          background: rgba(255, 255, 255, 0.1);
          color: #fff;
          border: 1px solid rgba(255, 255, 255, 0.3);
          padding: 1rem 1.5rem;
          border-radius: 25px;
          font-weight: 600;
          font-size: 1rem;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          gap: 0.5rem;
        ">
          <i class="fas fa-times"></i>
          Close
        </button>
      </div>
    `;

    // Add cancellation animations if not already present
    if (!document.getElementById('cancellation-styles')) {
      const style = document.createElement('style');
      style.id = 'cancellation-styles';
      style.textContent = `
        @keyframes cancellationBounce {
          0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
          50% { transform: scale(1.05) rotate(2deg); }
          70% { transform: scale(0.95) rotate(-1deg); }
          100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }
        @keyframes cancellationGlow {
          0%, 100% { text-shadow: 0 0 20px #ffd700, 0 0 40px #ffd700; }
          50% { text-shadow: 0 0 30px #ffd700, 0 0 60px #ffd700, 0 0 80px #ffd700; }
        }
      `;
      document.head.appendChild(style);
    }

    // Add close event listeners
    modal.addEventListener('click', (e) => {
      if (e.target === modal || e.target.classList.contains('close-modal')) {
        modal.remove();
      }
    });

    document.body.appendChild(modal);
    
    // Prevent accidental closure and ensure visibility
    setTimeout(() => {
      if (modal && modal.parentNode) {
        modal.style.display = 'flex';
        modal.style.opacity = '1';
        modal.style.visibility = 'visible';
      }
    }, 100);
  }

  /**
   * Initialize digital wallet payment methods
   */
  async initializeDigitalWallets(payments, order, tier) {
    try {
      console.log('🏦 Initializing digital wallets...');
      
      // Calculate amount from order
      let amount = '0.00';
      if (order.totalMoney && order.totalMoney.amount) {
        amount = (order.totalMoney.amount / 100).toFixed(2);
      } else if (order.total_money && order.total_money.amount) {
        amount = (order.total_money.amount / 100).toFixed(2);
      }
      
      // Create payment request for digital wallets
      const paymentRequest = payments.paymentRequest({
        countryCode: 'CA',
        currencyCode: 'CAD',
        total: {
          amount: amount,
          label: 'Warcraft Arena Membership'
        }
      });
      
      // Initialize Apple Pay
      try {
        const applePay = await payments.applePay(paymentRequest);
        const applePayButton = document.getElementById('apple-pay-button');
        if (applePayButton) {
          // Attach Apple Pay to the button element
          await applePay.attach('#apple-pay-button');
          applePayButton.style.display = 'flex';
          applePayButton.addEventListener('click', async () => {
            await this.processDigitalWalletPayment(applePay, order, tier, 'Apple Pay');
          });
          console.log('✅ Apple Pay initialized and attached');
        }
      } catch (error) {
        console.log('ℹ️ Apple Pay not available:', error.message);
      }
      
      // Initialize Google Pay
      try {
        const googlePay = await payments.googlePay(paymentRequest);
        const googlePayButton = document.getElementById('google-pay-button');
        if (googlePayButton) {
          // Attach Google Pay to the button element
          await googlePay.attach('#google-pay-button');
          googlePayButton.style.display = 'flex';
          googlePayButton.addEventListener('click', async () => {
            await this.processDigitalWalletPayment(googlePay, order, tier, 'Google Pay');
          });
          console.log('✅ Google Pay initialized and attached');
        }
      } catch (error) {
        console.log('ℹ️ Google Pay not available:', error.message);
      }
      
      // Initialize Afterpay
      try {
        const afterpay = await payments.afterpayClearpay(paymentRequest);
        const afterpayButton = document.getElementById('afterpay-button');
        if (afterpayButton) {
          // Attach Afterpay to the button element
          await afterpay.attach('#afterpay-button');
          afterpayButton.style.display = 'flex';
          afterpayButton.addEventListener('click', async () => {
            await this.processDigitalWalletPayment(afterpay, order, tier, 'Afterpay');
          });
          console.log('✅ Afterpay initialized and attached');
        }
      } catch (error) {
        console.log('ℹ️ Afterpay not available:', error.message);
      }
      
    } catch (error) {
      console.error('Error initializing digital wallets:', error);
      // Don't throw - card payments should still work
    }
  }

  /**
   * Process digital wallet payment
   */
  async processDigitalWalletPayment(paymentMethod, order, tier, methodName) {
    try {
      console.log(`💳 Processing ${methodName} payment...`);
      
      // Check if payment method is properly attached
      if (!paymentMethod) {
        throw new Error(`${methodName} is not properly initialized`);
      }
      
      const tokenResult = await paymentMethod.tokenize();
      
      if (tokenResult.status === 'OK') {
        console.log(`✅ ${methodName} tokenization successful`);
        
        // Process payment with backend
        const response = await fetch('/api/membership/process-payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            orderId: order.id,
            sourceId: tokenResult.token,
            verificationToken: tokenResult.details?.verification_token,
            tier: tier,
            paymentMethod: methodName.toLowerCase().replace(' ', '_'),
            changeType: order.changeType || 'new'
          })
        });

        const data = await response.json();
        
        if (data.success) {
          // Close modal and show success
          const modal = document.querySelector('.modal, [style*="position: fixed"]');
          if (modal) modal.remove();
          
          await this.handlePaymentSuccess(data.paymentId, 'square');
        } else {
          throw new Error(data.message || 'Payment failed');
        }
      } else {
        console.error(`❌ ${methodName} tokenization failed:`, tokenResult.errors);
        const errorMessage = tokenResult.errors?.[0]?.detail || tokenResult.errors?.[0]?.message || 'Unknown error';
        throw new Error(`${methodName} payment failed: ${errorMessage}`);
      }
    } catch (error) {
      console.error(`Error processing ${methodName} payment:`, error);
      
      // Provide user-friendly error messages
      let userMessage = error.message;
      if (error.message.includes('not been attached')) {
        userMessage = `${methodName} is not available right now. Please try using a card instead.`;
      } else if (error.message.includes('User canceled') || error.message.includes('cancelled')) {
        userMessage = `${methodName} payment was cancelled.`;
      } else if (error.message.includes('not supported')) {
        userMessage = `${methodName} is not supported on this device. Please try using a card instead.`;
      }
      
      this.showError(userMessage);
    }
  }
}

// Create global instance
window.membershipManager = new MembershipManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MembershipManager;
} 