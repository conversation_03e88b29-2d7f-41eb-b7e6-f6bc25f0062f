const express = require('express');
const router = express.Router();
const { ensureAuthenticated } = require('../middleware/auth');
const auth = ensureAuthenticated; // Alias for consistency
const squarePaymentService = require('../services/squarePaymentService');
const coinbaseService = require('../services/coinbaseService');
const UserMembership = require('../models/UserMembership');

/**
 * GET /api/membership/tiers
 * Get available membership tiers and pricing
 */
router.get('/tiers', (req, res) => {
  try {
    const squareTiers = squarePaymentService.getTierDetails();
    const coinbaseTiers = coinbaseService.getTierPricing();
    
    // Combine pricing from all providers
    const tiers = {};
    for (let tier = 1; tier <= 4; tier++) {
      tiers[tier] = {
        name: squareTiers[tier].name,
        description: squareTiers[tier].description,
        images: squareTiers[tier].images,
        providers: {
          square: {
            price: squareTiers[tier].price / 100, // Convert cents to dollars
            priceInCents: squareTiers[tier].price
          },
          coinbase: {
            monthlyPrice: coinbaseTiers[tier].monthlyPrice
          }
        }
      };
    }
    
    res.json({
      success: true,
      tiers,
      supportedProviders: ['square', 'coinbase'],
      environment: 'sandbox'
    });
  } catch (error) {
    console.error('Error getting membership tiers:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to get membership tiers' 
    });
  }
});

/**
 * GET /api/membership/status
 * Get user's current membership status
 */
router.get('/status', ensureAuthenticated, async (req, res) => {
  try {
    const membership = await UserMembership.getOrCreateForUser(req.user.id);
    
    // Format response for unified MembershipManager
    const isActive = membership.isSubscriptionActive();
    const tier = membership.tier || 0;
    const tierName = membership.tierName || (tier > 0 ? `Hero Tier ${tier}` : 'No Hero Status');
    const unlockedImages = membership.getAccessibleImages() || [];
    
    console.log('🛡️ Membership status response:', {
      isActive,
      tier,
      tierName,
      unlockedImages,
      membershipTier: membership.tier,
      membershipIsActive: membership.isActive,
      subscriptionActive: membership.isSubscriptionActive()
    });
    
    res.json({
      success: true,
      membership: {
        isActive,
        tier,
        tierName,
        unlockedImages,
        subscriptionType: membership.subscriptionType,
        subscriptionStatus: membership.subscriptionStatus,
        paymentProvider: membership.paymentProvider,
        nextBillingDate: membership.nextBillingDate,
        lastPaymentDate: membership.lastPaymentDate,
        gracePeriodEndDate: membership.gracePeriodEndDate
      }
    });
  } catch (error) {
    console.error('Error getting membership status:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to get membership status' 
    });
  }
});

/**
 * POST /api/membership/create-payment
 * Create payment order (Square or Coinbase) with subscription billing
 */
router.post('/create-payment', ensureAuthenticated, async (req, res) => {
  try {
    console.log('🎯 Create payment request received:', {
      body: req.body,
      userId: req.user?.id,
      username: req.user?.username
    });

    const { tier, provider, changeType = 'new' } = req.body;
    
    if (!tier || tier < 1 || tier > 4) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tier specified'
      });
    }

    if (!['square', 'coinbase'].includes(provider)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment provider. Supported: square, coinbase'
      });
    }

    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'User not properly authenticated'
      });
    }

    const currentMembership = await UserMembership.getOrCreateForUser(req.user.id);
    let paymentAmount;
    let paymentType;
    
    if (changeType === 'upgrade' && currentMembership.tier > 0) {
      // Calculate prorated upgrade amount
      const proration = currentMembership.calculateProratedUpgrade(tier);
      paymentAmount = proration.amount;
      paymentType = 'prorated_upgrade';
      
      if (paymentAmount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot upgrade to a lower or equal tier'
        });
      }
    } else {
      // New subscription - charge full monthly amount
      const tierBenefits = UserMembership.getTierBenefits(tier);
      paymentAmount = tierBenefits.monthlyPrice;
      paymentType = 'subscription';
    }
    
    let result;
    
    if (provider === 'square') {
      // Create Square order with calculated amount
      result = await squarePaymentService.createOrder(req.user.id, tier, paymentAmount);
    } else if (provider === 'coinbase') {
      result = await coinbaseService.createCharge(req.user.id, tier, paymentAmount);
    }
    
    res.json({
      success: true,
      provider,
      subscriptionType: 'monthly',
      orderId: result.id,
      totalAmount: paymentAmount,
      currency: 'USD',
      paymentType,
      changeType,
      currentTier: currentMembership.tier,
      newTier: tier,
      result
    });
  } catch (error) {
    console.error('Error creating payment:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create payment'
    });
  }
});

/**
 * POST /api/membership/process-payment
 * Process Square payment completion with subscription billing
 */
router.post('/process-payment', ensureAuthenticated, async (req, res) => {
  try {
    const { orderId, sourceId, verificationToken, tier, changeType = 'new', paymentMethod = 'card' } = req.body;
    
    if (!orderId || !sourceId || !tier) {
      return res.status(400).json({
        success: false,
        message: 'Missing required payment information (orderId, sourceId, tier)'
      });
    }

    // Process Square payment first
    const paymentResult = await squarePaymentService.processPayment(
      orderId, 
      sourceId, 
      verificationToken, 
      req.user.id, 
      tier
    );
    
    // Update membership based on change type
    const membership = await UserMembership.getOrCreateForUser(req.user.id);
    
    const paymentData = {
      provider: 'square',
      transactionId: paymentResult.id,
      orderId: orderId
    };
    
    let resultMessage;
    
    if (changeType === 'upgrade' && membership.tier > 0) {
      // Immediate upgrade with prorated charge
      await membership.upgradeSubscription(tier, paymentData);
      resultMessage = `Upgraded to ${membership.tierName}! You now have access to all ${membership.unlockedImages.length} premium profile images.`;
    } else {
      // New subscription
      await membership.startSubscription(tier, paymentData);
      const methodText = paymentMethod === 'card' ? 'card' : paymentMethod;
      resultMessage = `Welcome to ${membership.tierName}! Your 30-day subscription is now active. Payment processed via ${methodText}.`;
    }
    
    res.json({
      success: true,
      paymentId: paymentResult.id,
      orderId: orderId,
      provider: 'square',
      membership: {
        tier: membership.tier,
        tierName: membership.tierName,
        unlockedImages: membership.unlockedImages,
        currentPeriodEnd: membership.currentPeriodEnd,
        nextBillingDate: membership.nextBillingDate
      },
      message: resultMessage
    });
  } catch (error) {
    console.error('Error processing Square payment:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Payment processing failed'
    });
  }
});

/**
 * POST /api/membership/webhook/square
 * Handle Square webhooks
 */
router.post('/webhook/square', async (req, res) => {
  try {
    const event = req.body;
    
    // In production, verify webhook signature here
    await squarePaymentService.handleWebhook(event);
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error handling Square webhook:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Square webhook processing failed' 
    });
  }
});

/**
 * POST /api/membership/webhook/coinbase
 * Handle Coinbase webhooks
 */
router.post('/webhook/coinbase', async (req, res) => {
  try {
    const event = req.body;
    const signature = req.headers['x-cc-webhook-signature'];
    
    // Verify webhook signature in production
    // const isValid = coinbaseService.verifyWebhookSignature(req.rawBody, signature);
    // if (!isValid) {
    //   return res.status(401).json({ success: false, message: 'Invalid signature' });
    // }
    
    await coinbaseService.handleWebhook(event);
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error handling Coinbase webhook:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Coinbase webhook processing failed' 
    });
  }
});

/**
 * GET /api/membership/history
 * Get user's membership purchase history
 */
router.get('/history', ensureAuthenticated, async (req, res) => {
  try {
    const membership = await UserMembership.findOne({ user: req.user.id });
    
    if (!membership) {
      return res.json({
        success: true,
        history: []
      });
    }

    res.json({
      success: true,
      history: membership.paymentHistory || []
    });
  } catch (error) {
    console.error('Error getting membership history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get membership history'
    });
  }
});

/**
 * POST /api/membership/cancel
 * Cancel subscription (keeps access until period end)
 */
router.post('/cancel', ensureAuthenticated, async (req, res) => {
  try {
    const { reason = 'User requested cancellation' } = req.body;
    
    const membership = await UserMembership.findOne({ user: req.user.id });
    if (!membership || membership.tier === 0) {
      return res.status(400).json({
        success: false,
        message: 'No active membership found'
      });
    }

    if (membership.subscriptionStatus === 'cancelled') {
      return res.status(400).json({
        success: false,
        message: 'Subscription is already cancelled'
      });
    }

    // Cancel subscription but keep access until period end
    await membership.cancelSubscription(reason);
    
    res.json({
      success: true,
      message: `Subscription cancelled. You will retain ${membership.tierName} access until ${membership.willCancelAt.toLocaleDateString()}.`,
      retainAccessUntil: membership.willCancelAt,
      currentTier: membership.tier,
      tierName: membership.tierName
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel subscription'
    });
  }
});

/**
 * POST /api/membership/schedule-downgrade
 * Schedule a downgrade to take effect at period end
 */
router.post('/schedule-downgrade', ensureAuthenticated, async (req, res) => {
  try {
    const { newTier } = req.body;
    
    if (!newTier || newTier < 0 || newTier > 4) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tier specified'
      });
    }

    const membership = await UserMembership.findOne({ user: req.user.id });
    if (!membership || membership.tier === 0) {
      return res.status(400).json({
        success: false,
        message: 'No active membership found'
      });
    }

    if (newTier >= membership.tier) {
      return res.status(400).json({
        success: false,
        message: 'Cannot downgrade to a higher or equal tier. Use upgrade instead.'
      });
    }

    // Schedule the downgrade
    await membership.scheduleDowngrade(newTier);
    
    const newTierName = UserMembership.getTierBenefits(newTier).name;
    
    res.json({
      success: true,
      message: `Downgrade scheduled. You will switch to ${newTierName} on ${membership.pendingTierChange.effectiveDate.toLocaleDateString()}.`,
      currentTier: membership.tier,
      currentTierName: membership.tierName,
      pendingTier: newTier,
      pendingTierName: newTierName,
      effectiveDate: membership.pendingTierChange.effectiveDate
    });
  } catch (error) {
    console.error('Error scheduling downgrade:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to schedule downgrade'
    });
  }
});

/**
 * POST /api/membership/calculate-upgrade
 * Calculate prorated cost for an upgrade
 */
router.post('/calculate-upgrade', ensureAuthenticated, async (req, res) => {
  try {
    const { newTier } = req.body;
    
    if (!newTier || newTier < 1 || newTier > 4) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tier specified'
      });
    }

    const membership = await UserMembership.findOne({ user: req.user.id });
    if (!membership) {
      return res.status(400).json({
        success: false,
        message: 'No membership found'
      });
    }

    if (newTier <= membership.tier) {
      return res.status(400).json({
        success: false,
        message: 'Cannot upgrade to a lower or equal tier'
      });
    }

    const proration = membership.calculateProratedUpgrade(newTier);
    const newTierBenefits = UserMembership.getTierBenefits(newTier);
    
    res.json({
      success: true,
      currentTier: membership.tier,
      currentTierName: membership.tierName,
      newTier: newTier,
      newTierName: newTierBenefits.name,
      proratedAmount: proration.amount,
      daysRemaining: proration.daysRemaining,
      priceDifference: proration.priceDifference,
      currentPrice: proration.currentPrice,
      newPrice: proration.newPrice,
      currentPeriodEnd: membership.currentPeriodEnd
    });
  } catch (error) {
    console.error('Error calculating upgrade cost:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate upgrade cost'
    });
  }
});

/**
 * POST /api/membership/change-tier
 * Upgrade or downgrade membership tier
 */
router.post('/change-tier', ensureAuthenticated, async (req, res) => {
  try {
    const { newTier, provider, subscriptionType = 'monthly' } = req.body;
    
    if (!newTier || newTier < 1 || newTier > 4) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tier specified'
      });
    }

    const membership = await UserMembership.findOne({ user: req.user.id });
    if (!membership) {
      return res.status(400).json({
        success: false,
        message: 'No membership found. Please create a new subscription.'
      });
    }

    if (membership.tier === newTier) {
      return res.status(400).json({
        success: false,
        message: 'You are already on this tier'
      });
    }

    // For tier changes, user needs to create a new payment
    // This will be handled by the create-payment endpoint
    res.json({
      success: true,
      message: 'Please create a new payment for the tier change',
      currentTier: membership.tier,
      newTier: newTier,
      action: newTier > membership.tier ? 'upgrade' : 'downgrade'
    });
  } catch (error) {
    console.error('Error changing tier:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to change tier'
    });
  }
});

/**
 * POST /api/membership/check-image-access
 * Check if user has access to specific profile image
 */
router.post('/check-image-access', ensureAuthenticated, async (req, res) => {
  try {
    const { imageName } = req.body;
    
    if (!imageName) {
      return res.status(400).json({
        success: false,
        message: 'Image name is required'
      });
    }

    const membership = await UserMembership.getOrCreateForUser(req.user.id);
    const hasAccess = membership.hasImageAccess(imageName);
    
    res.json({
      success: true,
      hasAccess,
      tier: membership.tier,
      betaAccess: membership.betaAccess
    });
  } catch (error) {
    console.error('Error checking image access:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check image access'
    });
  }
});

/**
 * POST /api/membership/admin/initialize-old-users
 * Initialize membership records for users created before the membership system
 */
router.post('/admin/initialize-old-users', async (req, res) => {
  try {
    // This should be protected by admin middleware in production
    const User = require('../models/User');

    // Find all users without membership records
    const usersWithoutMembership = await User.find({}).lean();
    let initializedCount = 0;
    let skippedCount = 0;

    for (const user of usersWithoutMembership) {
      try {
        // Check if membership already exists
        const existingMembership = await UserMembership.findOne({ user: user._id });

        if (!existingMembership) {
          // Create default membership record
          const membership = new UserMembership({
            user: user._id,
            tier: 0,
            tierName: 'None',
            unlockedImages: [],
            subscriptionType: 'monthly',
            subscriptionStatus: 'pending',
            betaAccess: true,
            isActive: true
          });

          await membership.save();
          initializedCount++;
          console.log(`✅ Initialized membership for user: ${user.username || user._id}`);
        } else {
          skippedCount++;
        }
      } catch (error) {
        console.error(`❌ Failed to initialize membership for user ${user._id}:`, error);
      }
    }

    res.json({
      success: true,
      message: `Initialized ${initializedCount} users, skipped ${skippedCount} existing records`,
      initializedCount,
      skippedCount,
      totalUsers: usersWithoutMembership.length
    });
  } catch (error) {
    console.error('Error initializing old users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize old users'
    });
  }
});

module.exports = router;