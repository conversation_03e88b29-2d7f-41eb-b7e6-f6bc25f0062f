/**
 * Centralized Admin Control Panel
 * Complete administrative control system for all project features
 */

import { ApiClient } from './ApiClient.js';
import { UIManager } from './UIManager.js';
import { GameManager } from './GameManager.js';
import { ClanManager } from './ClanManager.js';
import { TournamentManager } from './TournamentManager.js';
import { AchievementEngine } from '../core/AchievementEngine.js';

export class AdminControlPanel {
  constructor() {
    this.api = new ApiClient();
    this.ui = new UIManager();
    this.gameManager = new GameManager();
    this.clanManager = new ClanManager();
    this.tournamentManager = new TournamentManager(this.api);
    this.achievementEngine = new AchievementEngine();
    
    this.state = {
      currentSection: 'dashboard',
      isLoading: false,
      stats: null,
      selectedItems: new Set()
    };
    
    this.sections = {
      dashboard: 'Dashboard',
      users: 'User Management',
      matches: 'Match Management',
      tournaments: 'Tournament Management',
      clans: 'Clan Management',
      maps: 'Map Management',
      achievements: 'Achievement System',
      disputes: 'Dispute Resolution',
      content: 'Content Management',
      system: 'System Settings',
      'image-cache': 'Image Cache Management',
      analytics: 'Analytics & Reports',
      moderation: 'Moderation Tools'
    };
  }

  /**
   * Initialize the admin control panel
   */
  async init() {
    try {
      console.log('🚀 Starting admin control panel initialization...');
      
      // Check admin permissions first
      console.log('🔐 Checking admin permissions...');
      const user = await this.checkAdminPermissions();
      console.log('✅ Admin permissions verified for user:', user);
      
      // Initialize UI components
      console.log('🎨 Initializing UI components...');
      this.setupEventListeners();
      
      // Check impersonation status
      console.log('🎭 Checking impersonation status...');
      await this.checkImpersonationStatus();
      
      // Load navigation counts
      console.log('📊 Loading navigation counts...');
      await this.loadNavigationCounts();

      // Load initial dashboard
      console.log('📊 Loading dashboard...');
      await this.navigateToSection('dashboard');

      console.log('✅ Admin control panel initialization complete!');
      
    } catch (error) {
      console.error('❌ Admin control panel initialization failed:', error);
      
      // Show detailed error information
      const contentContainer = document.getElementById('admin-content');
      if (contentContainer) {
        contentContainer.innerHTML = `
          <div class="error-state">
            <i class="fas fa-exclamation-triangle"></i>
            <h2>Initialization Failed</h2>
            <p><strong>Error:</strong> ${error.message}</p>
            <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px; font-family: monospace; font-size: 0.875rem; color: #666;">
              <strong>Debug Info:</strong><br>
              Current URL: ${window.location.href}<br>
              User Agent: ${navigator.userAgent.substring(0, 100)}...<br>
              Timestamp: ${new Date().toISOString()}
            </div>
            <div style="margin-top: 1rem;">
              <button class="btn btn-primary" onclick="window.location.reload()">
                <i class="fas fa-sync"></i> Retry
              </button>
              <a href="/index.html" class="btn btn-secondary" style="margin-left: 0.5rem;">
                <i class="fas fa-home"></i> Return to Home
              </a>
            </div>
          </div>
        `;
      }
      
      throw error;
    }
  }

  /**
   * Check admin permissions
   */
  async checkAdminPermissions() {
    try {
      console.log('🔐 Calling getCurrentUser()...');
      const user = await this.api.getCurrentUser();
      console.log('🔍 Raw user response:', user);
      console.log('🔍 User type:', typeof user);
      console.log('🔍 User keys:', user ? Object.keys(user) : 'null');

      if (!user) {
        throw new Error('No user found - please log in');
      }

      // Check if user has admin or moderator role
      console.log('🔍 Checking role:', user.role);
      if (user.role !== 'admin' && user.role !== 'moderator') {
        throw new Error(`Access denied. Admin or moderator privileges required. Current role: ${user.role}`);
      }

      console.log(`✅ User ${user.username} has ${user.role} access`);
      return user;
      
    } catch (error) {
      console.error('Admin permission check failed:', error);
      
      // Show more specific error messages
      if (error.message.includes('fetch')) {
        throw new Error('Unable to connect to server. Please check your connection.');
      } else if (error.message.includes('401') || error.message.includes('403')) {
        throw new Error('Access denied. Please log in with admin privileges.');
      } else {
        throw new Error(error.message || 'Authentication failed. Please try logging in again.');
      }
    }
  }

  /**
   * Load navigation counts from API
   */
  async loadNavigationCounts() {
    try {
      console.log('Loading navigation counts...');

      // First test if we can access the API at all
      try {
        const testResponse = await fetch('/api/admin/dashboard-stats', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        console.log('🔍 Direct fetch test - Status:', testResponse.status);
        console.log('🔍 Direct fetch test - Headers:', testResponse.headers);

        const testText = await testResponse.text();
        console.log('🔍 Direct fetch test - Response:', testText.substring(0, 200));
      } catch (testError) {
        console.error('🔍 Direct fetch test failed:', testError);
      }

      // Get dashboard stats which includes all the counts we need
      const response = await this.api.get('/admin/dashboard-stats');
      console.log('🔍 Dashboard stats response:', response);

      // Handle both wrapped and direct responses
      const stats = response.data || response;
      console.log('🔍 Processed stats:', stats);

      if (!stats || !stats.users) {
        console.warn('⚠️ Invalid stats structure:', stats);
        this.removeAllNavigationBadges();
        return;
      }

      // Update navigation badges with real data
      this.updateNavigationBadge('users', stats.users.total);
      this.updateNavigationBadge('matches', stats.matches.pending);
      this.updateNavigationBadge('disputes', stats.disputes.pending);
      this.updateNavigationBadge('moderation', stats.reports.pending);

      // Update sidebar quick stats
      this.updateSidebarStats({
        onlineUsers: stats.users.total, // Use total users as "online" for now
        activeMatches: stats.matches.pending,
        serverLoad: Math.round(stats.system.storage.usage)
      });

      console.log('✅ Navigation counts updated successfully');

    } catch (error) {
      console.warn('Failed to load navigation counts:', error.message);
      console.error('Full error:', error);
      // Remove all badges if we can't load real data
      this.removeAllNavigationBadges();
    }
  }

  /**
   * Update a specific navigation badge
   */
  updateNavigationBadge(section, count) {
    const navItem = document.querySelector(`[data-section="${section}"]`);
    if (navItem) {
      let badge = navItem.querySelector('.nav-badge');
      if (count > 0) {
        if (!badge) {
          badge = document.createElement('span');
          badge.className = 'nav-badge';
          navItem.appendChild(badge);
        }
        badge.textContent = count;

        // Add appropriate badge styling based on section
        badge.className = 'nav-badge';
        if (section === 'disputes' || section === 'moderation') {
          badge.classList.add(count > 0 ? 'danger' : 'info');
        } else if (section === 'matches') {
          badge.classList.add(count > 0 ? 'warning' : 'info');
        }
      } else if (badge) {
        // Remove badge if count is 0
        badge.remove();
      }
    }
  }

  /**
   * Update sidebar quick stats
   */
  updateSidebarStats(stats) {
    const sidebarStats = document.querySelector('.sidebar-stats');
    if (sidebarStats) {
      const statItems = sidebarStats.querySelectorAll('.stat-item');

      statItems.forEach(item => {
        const label = item.querySelector('.stat-label').textContent;
        const valueElement = item.querySelector('.stat-value');

        if (label.includes('Online Users') && stats.onlineUsers !== undefined) {
          valueElement.textContent = stats.onlineUsers;
        } else if (label.includes('Active Matches') && stats.activeMatches !== undefined) {
          valueElement.textContent = stats.activeMatches;
        } else if (label.includes('Server Load') && stats.serverLoad !== undefined) {
          valueElement.textContent = `${stats.serverLoad}%`;
        }
      });
    }
  }

  /**
   * Remove all navigation badges (fallback when API fails)
   */
  removeAllNavigationBadges() {
    document.querySelectorAll('.nav-badge').forEach(badge => {
      // Only remove badges that show counts, keep warning/danger badges for static items
      if (!badge.classList.contains('warning') && !badge.classList.contains('danger')) {
        badge.remove();
      }
    });
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Navigation - Use event delegation for better reliability
    document.addEventListener('click', (e) => {
      // Handle admin navigation
      const navItem = e.target.closest('.admin-nav-item');
      if (navItem) {
        e.preventDefault();
        const section = navItem.dataset.section;
        if (section) {
          console.log('Navigating to section:', section);
          this.navigateToSection(section);
        }
      }
    });

    // Bulk actions
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('bulk-select-all')) {
        this.handleBulkSelectAll(e.target.checked);
      } else if (e.target.classList.contains('bulk-select-item')) {
        this.handleBulkSelectItem(e.target.value, e.target.checked);
      }
    });

    // Initialize navigation items as active/inactive
    document.querySelectorAll('.admin-nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const section = item.dataset.section;
        if (section) {
          console.log('Direct nav click:', section);
          this.navigateToSection(section);
        }
      });
    });

    console.log('Admin event listeners setup completed');
  }

  /**
   * Navigate to admin section
   */
  async navigateToSection(section) {
    if (this.state.isLoading) {
      console.log('Navigation blocked - already loading');
      return;
    }

    try {
      console.log(`Navigating to section: ${section}`);
      this.state.isLoading = true;
      this.state.currentSection = section;
      
      // Show loading in content area
      const contentContainer = document.getElementById('admin-content');
      if (contentContainer) {
        contentContainer.innerHTML = `
          <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading ${this.sections[section] || section}...</p>
          </div>
        `;
      }
      
      // Update navigation - make sure we update all nav items
      document.querySelectorAll('.admin-nav-item').forEach(item => {
        const isActive = item.dataset.section === section;
        item.classList.toggle('active', isActive);
        console.log(`Nav item ${item.dataset.section}: ${isActive ? 'active' : 'inactive'}`);
      });

      // Load section content
      await this.loadSectionContent(section);
      console.log(`Successfully loaded section: ${section}`);

    } catch (error) {
      console.error(`Failed to load section ${section}:`, error);
      const contentContainer = document.getElementById('admin-content');
      if (contentContainer) {
        contentContainer.innerHTML = this.ui.createErrorState(
          'Loading Error', 
          `Failed to load ${this.sections[section]}: ${error.message}`
        );
      }
      this.ui.showError(`Failed to load ${this.sections[section]}`);
    } finally {
      this.state.isLoading = false;
    }
  }

  /**
   * Load section content
   */
  async loadSectionContent(section) {
    const contentContainer = document.getElementById('admin-content');
    
    switch (section) {
      case 'dashboard':
        await this.loadDashboard();
        break;
      case 'users':
        await this.loadUserManagement();
        break;
      case 'matches':
        await this.loadMatchManagement();
        break;
      case 'tournaments':
        await this.loadTournamentManagement();
        break;
      case 'clans':
        await this.loadClanManagement();
        break;
      case 'maps':
        await this.loadMapManagement();
        break;
      case 'achievements':
        await this.loadAchievementManagement();
        break;
      case 'disputes':
        await this.loadDisputeManagement();
        break;
      case 'content':
        await this.loadContentManagement();
        break;
      case 'system':
        await this.loadSystemSettings();
        break;
      case 'image-cache':
        await this.loadImageCacheManagement();
        break;
      case 'analytics':
        await this.loadAnalytics();
        break;
      case 'moderation':
        await this.loadModerationTools();
        break;
      default:
        contentContainer.innerHTML = this.ui.createErrorState('Section Not Found', 'The requested section could not be found.');
    }
  }

  /**
   * Load dashboard
   */
  async loadDashboard() {
    try {
      console.log('Loading admin dashboard...');
      
      // Try to get real stats, fallback to mock data if API doesn't exist
      let stats;
      try {
        stats = await this.api.get('/admin/dashboard-stats');
        console.log('Loaded real dashboard stats:', stats);
      } catch (error) {
        console.warn('Admin stats API not available, using mock data:', error.message);
        stats = this.getMockDashboardStats();
        console.log('Using mock dashboard stats:', stats);
      }
      this.state.stats = stats;

      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-dashboard">
          <div class="dashboard-header">
            <h1>Admin Dashboard</h1>
            <div class="dashboard-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.refreshDashboard()">
                <i class="fas fa-sync"></i> Refresh
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportSystemReport()">
                <i class="fas fa-download"></i> Export Report
              </button>
            </div>
          </div>

          <!-- Key Metrics -->
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="metric-content">
                <h3>${stats.users.total}</h3>
                <p>Total Users</p>
                <span class="metric-change ${stats.users.change >= 0 ? 'positive' : 'negative'}">
                  ${stats.users.change >= 0 ? '+' : ''}${stats.users.change}% this month
                </span>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-gamepad"></i>
              </div>
              <div class="metric-content">
                <h3>${stats.matches.total}</h3>
                <p>Total Matches</p>
                <span class="metric-change ${stats.matches.change >= 0 ? 'positive' : 'negative'}">
                  ${stats.matches.change >= 0 ? '+' : ''}${stats.matches.change}% this month
                </span>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-trophy"></i>
              </div>
              <div class="metric-content">
                <h3>${stats.tournaments.active}</h3>
                <p>Active Tournaments</p>
                <span class="metric-detail">${stats.tournaments.total} total</span>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="metric-content">
                <h3>${stats.clans.total}</h3>
                <p>Active Clans</p>
                <span class="metric-detail">${stats.clans.members} total members</span>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="dashboard-section">
            <h2>Quick Actions</h2>
            <div class="quick-actions-grid">
              <button class="quick-action-btn" onclick="adminControlPanel.navigateToSection('disputes')">
                <i class="fas fa-flag"></i>
                <span>Pending Disputes</span>
                <span class="badge">${stats.disputes.pending}</span>
              </button>
              
              <button class="quick-action-btn" onclick="adminControlPanel.navigateToSection('matches')">
                <i class="fas fa-clock"></i>
                <span>Pending Matches</span>
                <span class="badge">${stats.matches.pending}</span>
              </button>
              
              <button class="quick-action-btn" onclick="adminControlPanel.navigateToSection('users')">
                <i class="fas fa-user-plus"></i>
                <span>New Users</span>
                <span class="badge">${stats.users.newToday}</span>
              </button>
              
              <button class="quick-action-btn" onclick="adminControlPanel.navigateToSection('moderation')">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Reports</span>
                <span class="badge">${stats.reports.pending}</span>
              </button>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="dashboard-section">
            <h2>Recent Activity</h2>
            <div class="activity-feed">
              ${this.renderActivityFeed(stats.recentActivity)}
            </div>
          </div>

          <!-- System Status -->
          <div class="dashboard-section">
            <h2>System Status</h2>
            <div class="system-status-grid">
              <div class="status-item">
                <div class="status-indicator ${stats.system.database.status}"></div>
                <span>Database</span>
                <span class="status-value">${stats.system.database.responseTime}ms</span>
              </div>
              <div class="status-item">
                <div class="status-indicator ${stats.system.storage.status}"></div>
                <span>Storage</span>
                <span class="status-value">${stats.system.storage.usage}% used</span>
              </div>
              <div class="status-item">
                <div class="status-indicator ${stats.system.api.status}"></div>
                <span>API</span>
                <span class="status-value">${stats.system.api.uptime}% uptime</span>
              </div>
            </div>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Failed to load dashboard:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Dashboard Error', 'Failed to load dashboard data.');
    }
  }

  /**
   * Load user management
   */
  async loadUserManagement() {
    try {
      // Try to get real users, fallback to mock data if API doesn't exist
      let users;
      let pagination = null;
      try {
        console.log('🚀 Making API call to /admin/users...');
        const response = await this.api.get('/api/admin/users');
        console.log('🔍 Users API response:', response);
        console.log('🔍 Response type:', typeof response);
        console.log('🔍 Response keys:', Object.keys(response || {}));

        // Handle both wrapped and direct responses
        const data = response.data || response;
        console.log('🔍 Data after unwrapping:', data);
        console.log('🔍 Data type:', typeof data);
        console.log('🔍 Data keys:', Object.keys(data || {}));

        users = data.users || data; // Handle both paginated and direct array responses
        pagination = data.pagination;

        console.log('🔍 Final users variable:', users);
        console.log('🔍 Users type:', typeof users);
        console.log('🔍 Users is array:', Array.isArray(users));
        console.log('🔍 Users length:', users ? users.length : 'undefined');
        console.log('🔍 Pagination:', pagination);

        if (!Array.isArray(users)) {
          console.warn('⚠️ Users is not an array:', users);
          users = [];
        }
      } catch (error) {
        console.error('❌ Admin users API failed:', error);
        console.error('❌ Error message:', error.message);
        console.error('❌ Error stack:', error.stack);
        console.warn('Using mock data as fallback...');
        users = this.getMockUsers();
        console.log('🔍 Mock users:', users);
      }

      // Final debug before rendering
      console.log('🎯 About to render with users:', users);
      console.log('🎯 Users array check:', users && users.length > 0);
      console.log('🎯 Will show table:', !!(users && users.length > 0));

      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>User Management</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.createUser()">
                <i class="fas fa-plus"></i> Create User
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportUsers()">
                <i class="fas fa-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Filters -->
          <div class="filters-bar">
            <div class="filter-group">
              <label>Role:</label>
              <select id="user-role-filter">
                <option value="all">All Roles</option>
                <option value="admin">Admin</option>
                <option value="moderator">Moderator</option>
                <option value="user">User</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Status:</label>
              <select id="user-status-filter">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="suspended">Suspended</option>
                <option value="banned">Banned</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Search:</label>
              <input type="text" id="user-search" placeholder="Search users...">
            </div>
          </div>

          <!-- Bulk Actions -->
          <div class="bulk-actions">
            <label>
              <input type="checkbox" class="bulk-select-all"> Select All
            </label>
            <button class="btn btn-sm btn-warning" onclick="adminControlPanel.bulkSuspendUsers()">
              <i class="fas fa-pause"></i> Suspend
            </button>
            <button class="btn btn-sm btn-danger" onclick="adminControlPanel.bulkBanUsers()">
              <i class="fas fa-ban"></i> Ban
            </button>
            <button class="btn btn-sm btn-success" onclick="adminControlPanel.bulkActivateUsers()">
              <i class="fas fa-check"></i> Activate
            </button>
          </div>

          <!-- Users Table -->
          <div class="data-table-container">
            ${users && users.length > 0 ? `
              <table class="data-table">
                <thead>
                  <tr>
                    <th><input type="checkbox" class="bulk-select-all"></th>
                    <th>User</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Joined</th>
                    <th>Last Active</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  ${users.map(user => `
                    <tr>
                      <td><input type="checkbox" class="bulk-select-item" value="${user._id}"></td>
                      <td>
                        <div class="user-info">
                          <img src="${user.avatar || '/assets/img/default-avatar.svg'}" alt="${user.username}" class="user-avatar">
                          <div>
                            <div class="user-name">${user.username}</div>
                            <div class="user-id">#${user._id.slice(-6)}</div>
                          </div>
                        </div>
                      </td>
                      <td>${user.email}</td>
                      <td><span class="role-badge ${user.role}">${user.role}</span></td>
                      <td><span class="status-badge ${user.accountStatus || user.status || 'active'}">${user.accountStatus || user.status || 'active'}</span></td>
                      <td>${this.ui.formatDate(user.createdAt)}</td>
                      <td>${user.lastActive ? this.ui.formatDate(user.lastActive) : 'Never'}</td>
                      <td>
                        <div class="action-buttons">
                          <button class="btn btn-sm btn-primary" onclick="adminControlPanel.editUser('${user._id}')" title="Edit User">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-info" onclick="adminControlPanel.manageUserPermissions('${user._id}')" title="Manage Permissions">
                            <i class="fas fa-key"></i>
                          </button>
                          <button class="btn btn-sm btn-warning" onclick="adminControlPanel.banUser('${user._id}')" title="Ban User">
                            <i class="fas fa-ban"></i>
                          </button>
                          <button class="btn btn-sm btn-success" onclick="adminControlPanel.viewUserDetails('${user._id}')" title="View Details">
                            <i class="fas fa-eye"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            ` : `
              <div class="no-records-found">
                <div class="no-records-icon">
                  <i class="fas fa-users"></i>
                </div>
                <h3>No Users Found</h3>
                <p>There are currently no users in the system. Create the first user to get started.</p>
                <button class="btn btn-primary" onclick="adminControlPanel.createUser()">
                  <i class="fas fa-plus"></i> Create First User
                </button>
              </div>
            `}
          </div>
        </div>
      `;

      this.setupUserFilters(users);

    } catch (error) {
      console.error('Failed to load user management:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('User Management Error', 'Failed to load user data.');
    }
  }

  /**
   * Load match management
   */
  async loadMatchManagement() {
    try {
      const matches = await this.api.get('/admin/matches');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Match Management</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.createMatch()">
                <i class="fas fa-plus"></i> Create Match
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportMatches()">
                <i class="fas fa-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Match Filters -->
          <div class="filters-bar">
            <div class="filter-group">
              <label>Status:</label>
              <select id="match-status-filter">
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="verified">Verified</option>
                <option value="disputed">Disputed</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Type:</label>
              <select id="match-type-filter">
                <option value="all">All Types</option>
                <option value="1v1">1v1</option>
                <option value="2v2">2v2</option>
                <option value="3v3">3v3</option>
                <option value="4v4">4v4</option>
                <option value="ffa">FFA</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Game:</label>
              <select id="match-game-filter">
                <option value="all">All Games</option>
                <option value="warcraft2">Warcraft 2</option>
                <option value="warcraft3">Warcraft 3</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Date Range:</label>
              <input type="date" id="match-date-from">
              <input type="date" id="match-date-to">
            </div>
          </div>

          <!-- Bulk Actions -->
          <div class="bulk-actions">
            <label>
              <input type="checkbox" class="bulk-select-all"> Select All
            </label>
            <button class="btn btn-sm btn-success" onclick="adminControlPanel.bulkVerifyMatches()">
              <i class="fas fa-check"></i> Verify
            </button>
            <button class="btn btn-sm btn-danger" onclick="adminControlPanel.bulkRejectMatches()">
              <i class="fas fa-times"></i> Reject
            </button>
            <button class="btn btn-sm btn-warning" onclick="adminControlPanel.bulkDeleteMatches()">
              <i class="fas fa-trash"></i> Delete
            </button>
          </div>

          <!-- Matches Table -->
          <div class="data-table-container">
            ${matches && matches.length > 0 ? `
              <table class="data-table">
                <thead>
                  <tr>
                    <th><input type="checkbox" class="bulk-select-all"></th>
                    <th>Match</th>
                    <th>Players</th>
                    <th>Map</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Screenshots</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  ${matches.map(match => `
                    <tr>
                      <td><input type="checkbox" class="bulk-select-item" value="${match._id}"></td>
                      <td>
                        <div class="match-info">
                          <div class="match-type">${match.matchType}</div>
                          <div class="match-id">#${match._id.slice(-6)}</div>
                        </div>
                      </td>
                      <td>
                        <div class="match-players">
                          ${match.players.map(player => `
                            <span class="player ${match.winner === player.name ? 'winner' : ''}">${player.name}</span>
                          `).join(' vs ')}
                        </div>
                      </td>
                      <td>${match.map?.name || 'Unknown'}</td>
                      <td>${this.ui.formatDate(match.date)}</td>
                      <td><span class="status-badge ${match.verification?.status || 'pending'}">${match.verification?.status || 'pending'}</span></td>
                      <td>
                        <span class="screenshot-count">${match.screenshots?.length || 0}</span>
                        ${match.screenshots?.length > 0 ? `<button class="btn btn-sm btn-link" onclick="adminControlPanel.viewMatchScreenshots('${match._id}')">View</button>` : ''}
                      </td>
                      <td>
                        <div class="action-buttons">
                          <button class="btn btn-sm btn-primary" onclick="adminControlPanel.editMatch('${match._id}')">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-success" onclick="adminControlPanel.verifyMatch('${match._id}')">
                            <i class="fas fa-check"></i>
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="adminControlPanel.rejectMatch('${match._id}')">
                            <i class="fas fa-times"></i>
                          </button>
                          <button class="btn btn-sm btn-warning" onclick="adminControlPanel.deleteMatch('${match._id}')">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            ` : `
              <div class="no-records-found">
                <div class="no-records-icon">
                  <i class="fas fa-gamepad"></i>
                </div>
                <h3>No Matches Found</h3>
                <p>There are currently no matches in the system. Matches will appear here once users start reporting them.</p>
                <button class="btn btn-primary" onclick="adminControlPanel.createMatch()">
                  <i class="fas fa-plus"></i> Report First Match
                </button>
              </div>
            `}
          </div>
        </div>
      `;

      this.setupMatchFilters(matches);

    } catch (error) {
      console.error('Failed to load match management:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Match Management Error', 'Failed to load match data.');
    }
  }

  /**
   * Load dispute management
   */
  async loadDisputeManagement() {
    try {
      const response = await this.api.get('/admin/disputes');
      const disputes = response.disputes || [];
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Dispute Resolution</h1>
            <div class="section-actions">
              <button class="btn btn-secondary" onclick="adminControlPanel.exportDisputes()">
                <i class="fas fa-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Dispute Filters -->
          <div class="filters-bar">
            <div class="filter-group">
              <label>Status:</label>
              <select id="dispute-status-filter">
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="investigating">Investigating</option>
                <option value="resolved">Resolved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Type:</label>
              <select id="dispute-type-filter">
                <option value="all">All Types</option>
                <option value="incorrect_result">Incorrect Result</option>
                <option value="incorrect_players">Incorrect Players</option>
                <option value="fake_match">Fake Match</option>
                <option value="technical_issue">Technical Issue</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Priority:</label>
              <select id="dispute-priority-filter">
                <option value="all">All Priorities</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
          </div>

          <!-- Disputes Table -->
          <div class="data-table-container">
            ${disputes.length === 0 ? 
              `<div class="empty-state">
                <i class="fas fa-balance-scale"></i>
                <h3>No Disputes Found</h3>
                <p>There are no disputes to review at the moment.</p>
              </div>` : 
              `<table class="data-table">
                <thead>
                  <tr>
                    <th>Dispute</th>
                    <th>Match</th>
                    <th>Reporter</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>Priority</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  ${disputes.map(dispute => `
                    <tr>
                      <td>
                        <div class="dispute-info">
                          <div class="dispute-id">#${dispute._id.slice(-6)}</div>
                          <div class="dispute-summary">${(dispute.reason || 'No reason provided').substring(0, 50)}...</div>
                        </div>
                      </td>
                      <td>
                        <div class="match-link">
                          <span>${dispute.match?.matchType || 'Unknown'}</span>
                          <button class="btn btn-sm btn-link" onclick="adminControlPanel.viewMatch('${dispute.matchId}')">
                            View Match
                          </button>
                        </div>
                      </td>
                      <td>${dispute.reportedBy?.username || 'Unknown'}</td>
                      <td><span class="type-badge ${dispute.disputeType || 'other'}">${(dispute.disputeType || 'other').replace('_', ' ')}</span></td>
                      <td><span class="status-badge ${dispute.status || 'pending'}">${dispute.status || 'pending'}</span></td>
                      <td><span class="priority-badge ${dispute.priority || 'medium'}">${dispute.priority || 'medium'}</span></td>
                      <td>${this.ui.formatDate(dispute.createdAt)}</td>
                      <td>
                        <div class="action-buttons">
                          <button class="btn btn-sm btn-primary" onclick="adminControlPanel.reviewDispute('${dispute._id}')">
                            <i class="fas fa-gavel"></i> Review
                          </button>
                          <button class="btn btn-sm btn-success" onclick="adminControlPanel.resolveDispute('${dispute._id}')">
                            <i class="fas fa-check"></i> Resolve
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="adminControlPanel.rejectDispute('${dispute._id}')">
                            <i class="fas fa-times"></i> Reject
                          </button>
                        </div>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>`
            }
          </div>
        </div>
      `;

      this.setupDisputeFilters(disputes);

    } catch (error) {
      console.error('Failed to load dispute management:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Dispute Management Error', 'Failed to load dispute data.');
    }
  }

  /**
   * Load map management
   */
  async loadMapManagement() {
    try {
      const maps = await this.api.get('/admin/maps');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Map Management</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.uploadMap()">
                <i class="fas fa-upload"></i> Upload Map
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportMaps()">
                <i class="fas fa-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Map Filters -->
          <div class="filters-bar">
            <div class="filter-group">
              <label>Type:</label>
              <select id="map-type-filter">
                <option value="all">All Types</option>
                <option value="melee">Melee</option>
                <option value="custom">Custom</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Size:</label>
              <select id="map-size-filter">
                <option value="all">All Sizes</option>
                <option value="32x32">32x32</option>
                <option value="64x64">64x64</option>
                <option value="96x96">96x96</option>
                <option value="128x128">128x128</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Status:</label>
              <select id="map-status-filter">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="disabled">Disabled</option>
                <option value="pending">Pending Review</option>
              </select>
            </div>
          </div>

          <!-- Bulk Actions -->
          <div class="bulk-actions">
            <label>
              <input type="checkbox" class="bulk-select-all"> Select All
            </label>
            <button class="btn btn-sm btn-success" onclick="adminControlPanel.bulkActivateMaps()">
              <i class="fas fa-check"></i> Activate
            </button>
            <button class="btn btn-sm btn-warning" onclick="adminControlPanel.bulkDisableMaps()">
              <i class="fas fa-pause"></i> Disable
            </button>
            <button class="btn btn-sm btn-danger" onclick="adminControlPanel.bulkDeleteMaps()">
              <i class="fas fa-trash"></i> Delete
            </button>
          </div>

          <!-- Maps Grid -->
          <div class="maps-admin-grid">
            ${maps.map(map => `
              <div class="map-admin-card">
                <div class="map-selection">
                  <input type="checkbox" class="bulk-select-item" value="${map._id}">
                </div>
                <div class="map-thumbnail">
                  ${map.thumbnailPath ? 
                    `<img src="${map.thumbnailPath}" alt="${map.name}">` : 
                    `<div class="map-placeholder"><i class="fas fa-map"></i></div>`
                  }
                </div>
                <div class="map-info">
                  <h4>${map.name}</h4>
                  <div class="map-details">
                    <span class="map-type">${map.type}</span>
                    <span class="map-size">${map.size}</span>
                  </div>
                  <div class="map-stats">
                    <span><i class="fas fa-play"></i> ${map.playCount || 0}</span>
                    <span><i class="fas fa-download"></i> ${map.downloadCount || 0}</span>
                    <span><i class="fas fa-star"></i> ${map.averageRating ? map.averageRating.toFixed(1) : 'N/A'}</span>
                  </div>
                  <div class="map-status">
                    <span class="status-badge ${map.status || 'active'}">${map.status || 'active'}</span>
                  </div>
                </div>
                <div class="map-actions">
                  <button class="btn btn-sm btn-primary" onclick="adminControlPanel.editMap('${map._id}')">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button class="btn btn-sm btn-info" onclick="adminControlPanel.viewMapDetails('${map._id}')">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button class="btn btn-sm btn-warning" onclick="adminControlPanel.toggleMapStatus('${map._id}')">
                    <i class="fas fa-toggle-on"></i>
                  </button>
                  <button class="btn btn-sm btn-danger" onclick="adminControlPanel.deleteMap('${map._id}')">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      `;

      this.setupMapFilters(maps);

    } catch (error) {
      console.error('Failed to load map management:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Map Management Error', 'Failed to load map data.');
    }
  }

  /**
   * Load system settings
   */
  async loadSystemSettings() {
    try {
      const settings = await this.api.get('/admin/settings');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>System Settings</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.saveSettings()">
                <i class="fas fa-save"></i> Save Settings
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.resetSettings()">
                <i class="fas fa-undo"></i> Reset to Defaults
              </button>
            </div>
          </div>

          <div class="settings-container">
            <!-- General Settings -->
            <div class="settings-section">
              <h3>General Settings</h3>
              <div class="settings-grid">
                <div class="setting-item">
                  <label for="site-name">Site Name:</label>
                  <input type="text" id="site-name" name="siteName" value="${settings.general.siteName}" class="form-control">
                </div>
                <div class="setting-item">
                  <label for="site-description">Site Description:</label>
                  <textarea id="site-description" name="siteDescription" class="form-control">${settings.general.siteDescription}</textarea>
                </div>
                <div class="setting-item">
                  <label for="maintenance-mode">Maintenance Mode:</label>
                  <label class="toggle-switch">
                    <input type="checkbox" id="maintenance-mode" name="maintenanceMode" ${settings.general.maintenanceMode ? 'checked' : ''}>
                    <span class="toggle-slider"></span>
                  </label>
                </div>
                <div class="setting-item">
                  <label for="registration-enabled">User Registration:</label>
                  <label class="toggle-switch">
                    <input type="checkbox" id="registration-enabled" name="registrationEnabled" ${settings.general.registrationEnabled ? 'checked' : ''}>
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Match Settings -->
            <div class="settings-section">
              <h3>Match Settings</h3>
              <div class="settings-grid">
                <div class="setting-item">
                  <label for="auto-verify-matches">Auto-verify Matches:</label>
                  <label class="toggle-switch">
                    <input type="checkbox" id="auto-verify-matches" name="autoVerifyMatches" ${settings.matches.autoVerify ? 'checked' : ''}>
                    <span class="toggle-slider"></span>
                  </label>
                </div>
                <div class="setting-item">
                  <label for="match-edit-window">Edit Window (hours):</label>
                  <input type="number" id="match-edit-window" name="matchEditWindow" value="${settings.matches.editWindow}" min="1" max="168" class="form-control">
                </div>
                <div class="setting-item">
                  <label for="required-screenshots">Required Screenshots:</label>
                  <input type="number" id="required-screenshots" name="requiredScreenshots" value="${settings.matches.requiredScreenshots}" min="1" max="10" class="form-control">
                </div>
                <div class="setting-item">
                  <label for="max-file-size">Max File Size (MB):</label>
                  <input type="number" id="max-file-size" name="maxFileSize" value="${settings.matches.maxFileSize}" min="1" max="50" class="form-control">
                </div>
              </div>
            </div>

            <!-- Tournament Settings -->
            <div class="settings-section">
              <h3>Tournament Settings</h3>
              <div class="settings-grid">
                <div class="setting-item">
                  <label for="max-tournaments-per-user">Max Tournaments per User:</label>
                  <input type="number" id="max-tournaments-per-user" name="maxTournamentsPerUser" value="${settings.tournaments.maxPerUser}" min="1" max="10" class="form-control">
                </div>
                <div class="setting-item">
                  <label for="tournament-creation-cost">Creation Cost (Arena Gold):</label>
                  <input type="number" id="tournament-creation-cost" name="tournamentCreationCost" value="${settings.tournaments.creationCost}" min="0" class="form-control">
                </div>
                <div class="setting-item">
                  <label for="auto-start-tournaments">Auto-start when Full:</label>
                  <label class="toggle-switch">
                    <input type="checkbox" id="auto-start-tournaments" name="autoStartTournaments" ${settings.tournaments.autoStart ? 'checked' : ''}>
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Clan Settings -->
            <div class="settings-section">
              <h3>Clan Settings</h3>
              <div class="settings-grid">
                <div class="setting-item">
                  <label for="clan-creation-cost">Creation Cost (Arena Gold):</label>
                  <input type="number" id="clan-creation-cost" name="clanCreationCost" value="${settings.clans.creationCost}" min="0" class="form-control">
                </div>
                <div class="setting-item">
                  <label for="max-clan-members">Max Members per Clan:</label>
                  <input type="number" id="max-clan-members" name="maxClanMembers" value="${settings.clans.maxMembers}" min="5" max="100" class="form-control">
                </div>
                <div class="setting-item">
                  <label for="clan-tag-length">Max Tag Length:</label>
                  <input type="number" id="clan-tag-length" name="clanTagLength" value="${settings.clans.maxTagLength}" min="2" max="8" class="form-control">
                </div>
              </div>
            </div>

            <!-- Achievement Settings -->
            <div class="settings-section">
              <h3>Achievement Settings</h3>
              <div class="settings-grid">
                <div class="setting-item">
                  <label for="achievement-notifications">Enable Notifications:</label>
                  <label class="toggle-switch">
                    <input type="checkbox" id="achievement-notifications" name="achievementNotifications" ${settings.achievements.notifications ? 'checked' : ''}>
                    <span class="toggle-slider"></span>
                  </label>
                </div>
                <div class="setting-item">
                  <label for="retroactive-achievements">Retroactive Processing:</label>
                  <label class="toggle-switch">
                    <input type="checkbox" id="retroactive-achievements" name="retroactiveAchievements" ${settings.achievements.retroactive ? 'checked' : ''}>
                    <span class="toggle-slider"></span>
                  </label>
                </div>
                <div class="setting-item">
                  <label for="base-experience-reward">Base Experience Reward:</label>
                  <input type="number" id="base-experience-reward" name="baseExperienceReward" value="${settings.achievements.baseExperience}" min="1" class="form-control">
                </div>
              </div>
            </div>

            <!-- Database Backup -->
            <div class="settings-section">
              <h3>Database Backup</h3>
              <div class="backup-section">
                <div class="backup-actions">
                  <button class="btn btn-primary" onclick="adminControlPanel.createDatabaseBackup()">
                    <i class="fas fa-database"></i> Create Backup
                  </button>
                  <p class="backup-info">
                    <i class="fas fa-info-circle"></i>
                    Regular backups help protect your data. Backups are created using mongodump and stored locally.
                  </p>
                </div>
                <div id="backup-list" class="backup-list">
                  <!-- Backup list will be loaded here -->
                </div>
              </div>
            </div>
          </div>
        </div>
      `;

      // Load the backup list
      await this.refreshBackupList();

    } catch (error) {
      console.error('Failed to load system settings:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('System Settings Error', 'Failed to load settings.');
    }
  }

  /**
   * Render activity feed
   */
  renderActivityFeed(activities) {
    if (!activities || activities.length === 0) {
      return '<div class="no-activity">No recent activity</div>';
    }

    return activities.map(activity => `
      <div class="activity-item">
        <div class="activity-icon">
          <i class="fas ${this.getActivityIcon(activity.type)}"></i>
        </div>
        <div class="activity-content">
          <div class="activity-description">${activity.description}</div>
          <div class="activity-time">${this.ui.formatRelativeTime(activity.timestamp)}</div>
        </div>
      </div>
    `).join('');
  }

  /**
   * Get activity icon
   */
  getActivityIcon(type) {
    const icons = {
      'user_registered': 'fa-user-plus',
      'match_reported': 'fa-gamepad',
      'tournament_created': 'fa-trophy',
      'clan_created': 'fa-shield-alt',
      'dispute_filed': 'fa-flag',
      'map_uploaded': 'fa-map',
      'achievement_unlocked': 'fa-medal'
    };
    return icons[type] || 'fa-info-circle';
  }

  /**
   * Setup user filters
   */
  setupUserFilters(allUsers) {
    const roleFilter = document.getElementById('user-role-filter');
    const statusFilter = document.getElementById('user-status-filter');
    const searchInput = document.getElementById('user-search');

    const applyFilters = () => {
      // Implementation for filtering users
      console.log('Applying user filters');
    };

    roleFilter?.addEventListener('change', applyFilters);
    statusFilter?.addEventListener('change', applyFilters);
    searchInput?.addEventListener('input', applyFilters);
  }

  /**
   * Setup match filters
   */
  setupMatchFilters(allMatches) {
    // Implementation for match filters
    console.log('Setting up match filters');
  }

  /**
   * Setup dispute filters
   */
  setupDisputeFilters(allDisputes) {
    // Implementation for dispute filters
    console.log('Setting up dispute filters');
  }

  /**
   * Setup map filters
   */
  setupMapFilters(allMaps) {
    // Implementation for map filters
    console.log('Setting up map filters');
  }

  /**
   * Handle bulk select all
   */
  handleBulkSelectAll(checked) {
    document.querySelectorAll('.bulk-select-item').forEach(checkbox => {
      checkbox.checked = checked;
      if (checked) {
        this.state.selectedItems.add(checkbox.value);
      } else {
        this.state.selectedItems.delete(checkbox.value);
      }
    });
  }

  /**
   * Handle bulk select item
   */
  handleBulkSelectItem(value, checked) {
    if (checked) {
      this.state.selectedItems.add(value);
    } else {
      this.state.selectedItems.delete(value);
    }
  }

  /**
   * Refresh dashboard
   */
  async refreshDashboard() {
    await this.loadDashboard();
  }

  /**
   * Export system report
   */
  async exportSystemReport() {
    try {
      const response = await this.api.get('/admin/export/system-report');
      // Handle file download
      this.ui.showSuccess('System report exported successfully.');
    } catch (error) {
      console.error('Failed to export system report:', error);
      this.ui.showError('Failed to export system report.');
    }
  }

  // User Management Methods
  async createUser() {
    const modal = this.ui.createModal('create-user-modal', 'Create New User', `
      <form id="create-user-form" class="form">
        <div class="form-group">
          <label for="new-username">Username:</label>
          <input type="text" id="new-username" name="username" required>
        </div>
        <div class="form-group">
          <label for="new-email">Email:</label>
          <input type="email" id="new-email" name="email" required>
        </div>
        <div class="form-group">
          <label for="new-password">Password:</label>
          <input type="password" id="new-password" name="password" required>
        </div>
        <div class="form-group">
          <label for="new-role">Role:</label>
          <select id="new-role" name="role" required>
            <option value="user">User</option>
            <option value="moderator">Moderator</option>
            <option value="admin">Admin</option>
          </select>
        </div>
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
          <button type="submit" class="btn btn-primary">Create User</button>
        </div>
      </form>
    `);

    document.getElementById('create-user-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      try {
        const formData = new FormData(e.target);
        await this.api.post('/admin/users', Object.fromEntries(formData));
        this.ui.showSuccess('User created successfully');
        modal.remove();
        await this.loadUserManagement();
      } catch (error) {
        this.ui.showError(`Failed to create user: ${error.message}`);
      }
    });
  }

  async editUser(userId) {
    try {
      const user = await this.api.get(`/admin/users/${userId}`);
      const modal = this.ui.createModal('edit-user-modal', 'Edit User', `
        <form id="edit-user-form" class="form">
          <div class="form-group">
            <label for="edit-username">Username:</label>
            <input type="text" id="edit-username" name="username" value="${user.username}" required>
          </div>
          <div class="form-group">
            <label for="edit-email">Email:</label>
            <input type="email" id="edit-email" name="email" value="${user.email}" required>
          </div>
          <div class="form-group">
            <label for="edit-role">Role:</label>
            <select id="edit-role" name="role" required>
              <option value="user" ${user.role === 'user' ? 'selected' : ''}>User</option>
              <option value="moderator" ${user.role === 'moderator' ? 'selected' : ''}>Moderator</option>
              <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-status">Status:</label>
            <select id="edit-status" name="status" required>
              <option value="active" ${user.status === 'active' ? 'selected' : ''}>Active</option>
              <option value="suspended" ${user.status === 'suspended' ? 'selected' : ''}>Suspended</option>
              <option value="banned" ${user.status === 'banned' ? 'selected' : ''}>Banned</option>
            </select>
          </div>
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
            <button type="submit" class="btn btn-primary">Update User</button>
          </div>
        </form>
      `);

      document.getElementById('edit-user-form').addEventListener('submit', async (e) => {
        e.preventDefault();
        try {
          const formData = new FormData(e.target);
          await this.api.put(`/admin/users/${userId}`, Object.fromEntries(formData));
          this.ui.showSuccess('User updated successfully');
          modal.remove();
          await this.loadUserManagement();
        } catch (error) {
          this.ui.showError(`Failed to update user: ${error.message}`);
        }
      });
    } catch (error) {
      this.ui.showError(`Failed to load user details: ${error.message}`);
    }
  }

  async manageUserPermissions(userId) {
    try {
      const user = await this.api.get(`/admin/users/${userId}`);
      const permissions = user.permissions || {
        canReportMatches: true,
        canCreateTournaments: false,
        canUploadMaps: true,
        canUseChat: true,
        canCreateClans: true
      };

      const modal = this.ui.createModal('manage-permissions-modal', `Manage Permissions - ${user.username}`, `
        <form id="permissions-form" class="form">
          <div class="permissions-grid">
            <div class="permission-item">
              <label class="permission-label">
                <input type="checkbox" name="canReportMatches" ${permissions.canReportMatches ? 'checked' : ''}>
                <span class="permission-title">Can Report Matches</span>
                <small class="permission-desc">Allow user to report match results</small>
              </label>
            </div>

            <div class="permission-item">
              <label class="permission-label">
                <input type="checkbox" name="canCreateTournaments" ${permissions.canCreateTournaments ? 'checked' : ''}>
                <span class="permission-title">Can Create Tournaments</span>
                <small class="permission-desc">Allow user to create and manage tournaments</small>
              </label>
            </div>

            <div class="permission-item">
              <label class="permission-label">
                <input type="checkbox" name="canUploadMaps" ${permissions.canUploadMaps ? 'checked' : ''}>
                <span class="permission-title">Can Upload Maps</span>
                <small class="permission-desc">Allow user to upload new maps</small>
              </label>
            </div>

            <div class="permission-item">
              <label class="permission-label">
                <input type="checkbox" name="canUseChat" ${permissions.canUseChat ? 'checked' : ''}>
                <span class="permission-title">Can Use Chat</span>
                <small class="permission-desc">Allow user to participate in chat</small>
              </label>
            </div>

            <div class="permission-item">
              <label class="permission-label">
                <input type="checkbox" name="canCreateClans" ${permissions.canCreateClans ? 'checked' : ''}>
                <span class="permission-title">Can Create Clans</span>
                <small class="permission-desc">Allow user to create and manage clans</small>
              </label>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
            <button type="submit" class="btn btn-primary">Update Permissions</button>
          </div>
        </form>
      `);

      document.getElementById('permissions-form').addEventListener('submit', async (e) => {
        e.preventDefault();
        try {
          const formData = new FormData(e.target);
          const updatedPermissions = {
            canReportMatches: formData.has('canReportMatches'),
            canCreateTournaments: formData.has('canCreateTournaments'),
            canUploadMaps: formData.has('canUploadMaps'),
            canUseChat: formData.has('canUseChat'),
            canCreateClans: formData.has('canCreateClans')
          };

          await this.api.put(`/admin/users/${userId}/permissions`, { permissions: updatedPermissions });
          this.ui.showSuccess('User permissions updated successfully');
          modal.remove();
          await this.loadUserManagement();
        } catch (error) {
          this.ui.showError(`Failed to update permissions: ${error.message}`);
        }
      });
    } catch (error) {
      this.ui.showError(`Failed to load user permissions: ${error.message}`);
    }
  }

  async banUser(userId) {
    try {
      const user = await this.api.get(`/admin/users/${userId}`);
      const modal = this.ui.createModal('ban-user-modal', `Ban User - ${user.username}`, `
        <form id="ban-user-form" class="form">
          <div class="form-group">
            <label for="ban-reason">Reason for Ban:</label>
            <textarea id="ban-reason" name="reason" rows="3" placeholder="Enter reason for banning this user..." required></textarea>
          </div>

          <div class="form-group">
            <label for="ban-duration">Ban Duration:</label>
            <select id="ban-duration" name="duration">
              <option value="">Permanent Ban</option>
              <option value="24">24 Hours</option>
              <option value="72">3 Days</option>
              <option value="168">1 Week</option>
              <option value="720">30 Days</option>
            </select>
          </div>

          <div class="form-group">
            <label>
              <input type="checkbox" name="banIP" value="true">
              Also ban IP address
            </label>
          </div>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
            <button type="submit" class="btn btn-danger">Ban User</button>
          </div>
        </form>
      `);

      document.getElementById('ban-user-form').addEventListener('submit', async (e) => {
        e.preventDefault();
        try {
          const formData = new FormData(e.target);
          const banData = {
            reason: formData.get('reason'),
            duration: formData.get('duration') ? parseInt(formData.get('duration')) : null
          };

          if (formData.has('banIP')) {
            // Get user's IP addresses if available
            banData.ipAddresses = user.ipAddresses || [];
          }

          await this.api.put(`/admin/users/${userId}/ban`, banData);
          this.ui.showSuccess('User banned successfully');
          modal.remove();
          await this.loadUserManagement();
        } catch (error) {
          this.ui.showError(`Failed to ban user: ${error.message}`);
        }
      });
    } catch (error) {
      this.ui.showError(`Failed to load user details: ${error.message}`);
    }
  }

  async viewUserDetails(userId) {
    try {
      const user = await this.api.get(`/admin/users/${userId}/details`);
      this.ui.createModal('user-details-modal', 'User Details', `
        <div class="user-details-content">
          <div class="user-profile">
            <img src="${user.avatar || '/images/default-avatar.png'}" alt="${user.username}" class="user-avatar-large">
            <h3>${user.username}</h3>
            <p>${user.email}</p>
            <span class="role-badge ${user.role}">${user.role}</span>
            <span class="status-badge ${user.status}">${user.status}</span>
          </div>
          <div class="user-stats">
            <h4>Statistics</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <label>Matches Played:</label>
                <span>${user.stats.matchesPlayed}</span>
              </div>
              <div class="stat-item">
                <label>Wins:</label>
                <span>${user.stats.wins}</span>
              </div>
              <div class="stat-item">
                <label>Win Rate:</label>
                <span>${user.stats.winRate}%</span>
              </div>
              <div class="stat-item">
                <label>Current Rank:</label>
                <span>${user.stats.currentRank}</span>
              </div>
              <div class="stat-item">
                <label>Joined:</label>
                <span>${this.ui.formatDate(user.createdAt)}</span>
              </div>
              <div class="stat-item">
                <label>Last Active:</label>
                <span>${user.lastActive ? this.ui.formatDate(user.lastActive) : 'Never'}</span>
              </div>
            </div>
          </div>
        </div>
      `);
    } catch (error) {
      this.ui.showError(`Failed to load user details: ${error.message}`);
    }
  }

  async suspendUser(userId) {
    const confirmed = await this.ui.confirm('Suspend User', 'Are you sure you want to suspend this user?');
    if (!confirmed) return;

    try {
      await this.api.put(`/admin/users/${userId}/suspend`);
      this.ui.showSuccess('User suspended successfully');
      await this.loadUserManagement();
    } catch (error) {
      this.ui.showError(`Failed to suspend user: ${error.message}`);
    }
  }

  async deleteUser(userId) {
    const confirmed = await this.ui.confirm('Delete User', 'Are you sure you want to delete this user? This action cannot be undone.');
    if (!confirmed) return;

    try {
      await this.api.delete(`/admin/users/${userId}`);
      this.ui.showSuccess('User deleted successfully');
      await this.loadUserManagement();
    } catch (error) {
      this.ui.showError(`Failed to delete user: ${error.message}`);
    }
  }

  async bulkSuspendUsers() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select users to suspend');
      return;
    }

    const confirmed = await this.ui.confirm('Bulk Suspend', `Suspend ${this.state.selectedItems.size} selected users?`);
    if (!confirmed) return;

    try {
      await this.api.post('/admin/users/bulk-suspend', { userIds: Array.from(this.state.selectedItems) });
      this.ui.showSuccess(`${this.state.selectedItems.size} users suspended successfully`);
      this.state.selectedItems.clear();
      await this.loadUserManagement();
    } catch (error) {
      this.ui.showError(`Failed to suspend users: ${error.message}`);
    }
  }

  async bulkBanUsers() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select users to ban');
      return;
    }

    const confirmed = await this.ui.confirm('Bulk Ban', `Ban ${this.state.selectedItems.size} selected users?`);
    if (!confirmed) return;

    try {
      await this.api.post('/admin/users/bulk-ban', { userIds: Array.from(this.state.selectedItems) });
      this.ui.showSuccess(`${this.state.selectedItems.size} users banned successfully`);
      this.state.selectedItems.clear();
      await this.loadUserManagement();
    } catch (error) {
      this.ui.showError(`Failed to ban users: ${error.message}`);
    }
  }

  async bulkActivateUsers() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select users to activate');
      return;
    }

    const confirmed = await this.ui.confirm('Bulk Activate', `Activate ${this.state.selectedItems.size} selected users?`);
    if (!confirmed) return;

    try {
      await this.api.post('/admin/users/bulk-activate', { userIds: Array.from(this.state.selectedItems) });
      this.ui.showSuccess(`${this.state.selectedItems.size} users activated successfully`);
      this.state.selectedItems.clear();
      await this.loadUserManagement();
    } catch (error) {
      this.ui.showError(`Failed to activate users: ${error.message}`);
    }
  }

  // Match Management Methods
  async createMatch() {
    window.matchReportManager.showReportModal();
  }

  async editMatch(matchId) {
    window.matchDisputeManager.showEditMatchModal(matchId);
  }

  async verifyMatch(matchId) {
    const confirmed = await this.ui.confirm('Verify Match', 'Are you sure you want to verify this match?');
    if (!confirmed) return;

    try {
      await this.api.put(`/admin/matches/${matchId}/verify`);
      this.ui.showSuccess('Match verified successfully');
      await this.loadMatchManagement();
    } catch (error) {
      this.ui.showError(`Failed to verify match: ${error.message}`);
    }
  }

  async rejectMatch(matchId) {
    const reason = await this.ui.prompt('Reject Match', 'Please provide a reason for rejecting this match:');
    if (!reason) return;

    try {
      await this.api.put(`/admin/matches/${matchId}/reject`, { reason });
      this.ui.showSuccess('Match rejected successfully');
      await this.loadMatchManagement();
    } catch (error) {
      this.ui.showError(`Failed to reject match: ${error.message}`);
    }
  }

  async deleteMatch(matchId) {
    const confirmed = await this.ui.confirm('Delete Match', 'Are you sure you want to delete this match? This action cannot be undone.');
    if (!confirmed) return;

    try {
      await this.api.delete(`/admin/matches/${matchId}`);
      this.ui.showSuccess('Match deleted successfully');
      await this.loadMatchManagement();
    } catch (error) {
      this.ui.showError(`Failed to delete match: ${error.message}`);
    }
  }

  async viewMatchScreenshots(matchId) {
    try {
      const match = await this.api.get(`/admin/matches/${matchId}/screenshots`);
      this.ui.createModal('match-screenshots-modal', 'Match Screenshots', `
        <div class="screenshots-gallery">
          ${match.screenshots.map((screenshot, index) => `
            <div class="screenshot-item">
              <img src="${screenshot.url}" alt="Screenshot ${index + 1}" onclick="window.open('${screenshot.url}', '_blank')">
              <div class="screenshot-actions">
                <button class="btn btn-sm btn-danger" onclick="adminControlPanel.flagScreenshot('${screenshot.url}')">
                  <i class="fas fa-flag"></i> Flag
                </button>
              </div>
            </div>
          `).join('')}
        </div>
      `);
    } catch (error) {
      this.ui.showError(`Failed to load screenshots: ${error.message}`);
    }
  }

  async bulkVerifyMatches() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select matches to verify');
      return;
    }

    const confirmed = await this.ui.confirm('Bulk Verify', `Verify ${this.state.selectedItems.size} selected matches?`);
    if (!confirmed) return;

    try {
      await this.api.post('/admin/matches/bulk-verify', { matchIds: Array.from(this.state.selectedItems) });
      this.ui.showSuccess(`${this.state.selectedItems.size} matches verified successfully`);
      this.state.selectedItems.clear();
      await this.loadMatchManagement();
    } catch (error) {
      this.ui.showError(`Failed to verify matches: ${error.message}`);
    }
  }

  async bulkRejectMatches() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select matches to reject');
      return;
    }

    const reason = await this.ui.prompt('Bulk Reject', 'Please provide a reason for rejecting these matches:');
    if (!reason) return;

    try {
      await this.api.post('/admin/matches/bulk-reject', { 
        matchIds: Array.from(this.state.selectedItems),
        reason 
      });
      this.ui.showSuccess(`${this.state.selectedItems.size} matches rejected successfully`);
      this.state.selectedItems.clear();
      await this.loadMatchManagement();
    } catch (error) {
      this.ui.showError(`Failed to reject matches: ${error.message}`);
    }
  }

  async bulkDeleteMatches() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select matches to delete');
      return;
    }

    const confirmed = await this.ui.confirm('Bulk Delete', `Delete ${this.state.selectedItems.size} selected matches? This action cannot be undone.`);
    if (!confirmed) return;

    try {
      await this.api.post('/admin/matches/bulk-delete', { matchIds: Array.from(this.state.selectedItems) });
      this.ui.showSuccess(`${this.state.selectedItems.size} matches deleted successfully`);
      this.state.selectedItems.clear();
      await this.loadMatchManagement();
    } catch (error) {
      this.ui.showError(`Failed to delete matches: ${error.message}`);
    }
  }

  // Dispute Management Methods
  async reviewDispute(disputeId) {
    try {
      const dispute = await this.api.get(`/admin/disputes/${disputeId}`);
      this.ui.createModal('review-dispute-modal', 'Review Dispute', `
        <div class="dispute-review-content">
          <div class="dispute-info">
            <h3>Dispute #${dispute._id.slice(-6)}</h3>
            <div class="dispute-details">
              <div class="detail-item">
                <label>Type:</label>
                <span>${dispute.disputeType.replace('_', ' ')}</span>
              </div>
              <div class="detail-item">
                <label>Reporter:</label>
                <span>${dispute.reportedBy.username}</span>
              </div>
              <div class="detail-item">
                <label>Status:</label>
                <span class="status-badge ${dispute.status}">${dispute.status}</span>
              </div>
              <div class="detail-item">
                <label>Created:</label>
                <span>${this.ui.formatDate(dispute.createdAt)}</span>
              </div>
            </div>
          </div>
          
          <div class="dispute-reason">
            <h4>Reason</h4>
            <p>${dispute.reason}</p>
          </div>
          
          ${dispute.correctInfo ? `
            <div class="correct-info">
              <h4>Correct Information</h4>
              <p>${dispute.correctInfo}</p>
            </div>
          ` : ''}
          
          ${dispute.evidence && dispute.evidence.length > 0 ? `
            <div class="dispute-evidence">
              <h4>Evidence</h4>
              <div class="evidence-gallery">
                ${dispute.evidence.map((evidence, index) => `
                  <img src="${evidence.url}" alt="Evidence ${index + 1}" onclick="window.open('${evidence.url}', '_blank')">
                `).join('')}
              </div>
            </div>
          ` : ''}
          
          <div class="review-actions">
            <button class="btn btn-success" onclick="adminControlPanel.resolveDispute('${disputeId}')">
              <i class="fas fa-check"></i> Resolve
            </button>
            <button class="btn btn-danger" onclick="adminControlPanel.rejectDispute('${disputeId}')">
              <i class="fas fa-times"></i> Reject
            </button>
            <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">
              Close
            </button>
          </div>
        </div>
      `);
    } catch (error) {
      this.ui.showError(`Failed to load dispute: ${error.message}`);
    }
  }

  async resolveDispute(disputeId) {
    const resolution = await this.ui.prompt('Resolve Dispute', 'Please provide a resolution summary:');
    if (!resolution) return;

    try {
      await this.api.put(`/admin/disputes/${disputeId}/resolve`, { resolution });
      this.ui.showSuccess('Dispute resolved successfully');
      document.querySelector('.modal')?.remove();
      await this.loadDisputeManagement();
    } catch (error) {
      this.ui.showError(`Failed to resolve dispute: ${error.message}`);
    }
  }

  async rejectDispute(disputeId) {
    const reason = await this.ui.prompt('Reject Dispute', 'Please provide a reason for rejecting this dispute:');
    if (!reason) return;

    try {
      await this.api.put(`/admin/disputes/${disputeId}/reject`, { reason });
      this.ui.showSuccess('Dispute rejected successfully');
      document.querySelector('.modal')?.remove();
      await this.loadDisputeManagement();
    } catch (error) {
      this.ui.showError(`Failed to reject dispute: ${error.message}`);
    }
  }

  // Map Management Methods
  async uploadMap() {
    const modal = this.ui.createModal('upload-map-modal', 'Upload Map', `
      <form id="upload-map-form" class="form" enctype="multipart/form-data">
        <div class="form-group">
          <label for="map-name">Map Name:</label>
          <input type="text" id="map-name" name="name" required>
        </div>
        <div class="form-group">
          <label for="map-type">Type:</label>
          <select id="map-type" name="type" required>
            <option value="melee">Melee</option>
            <option value="custom">Custom</option>
          </select>
        </div>
        <div class="form-group">
          <label for="map-size">Size:</label>
          <select id="map-size" name="size" required>
            <option value="32x32">32x32</option>
            <option value="64x64">64x64</option>
            <option value="96x96">96x96</option>
            <option value="128x128">128x128</option>
          </select>
        </div>
        <div class="form-group">
          <label for="map-game-type">Game Type:</label>
          <select id="map-game-type" name="gameType" required>
            <option value="warcraft2">Warcraft 2</option>
            <option value="warcraft3">Warcraft 3</option>
          </select>
        </div>
        <div class="form-group">
          <label for="map-file">Map File:</label>
          <input type="file" id="map-file" name="mapFile" accept=".pud,.w3x,.w3m" required>
        </div>
        <div class="info-box" style="background: rgba(0,123,255,0.1); border: 1px solid rgba(0,123,255,0.3); padding: 1rem; border-radius: 8px; margin: 1rem 0;">
          <strong>✨ Auto-Generated Features:</strong>
          <ul style="margin: 0.5rem 0; padding-left: 1.5rem; color: #666;">
            <li>Map thumbnail - Generated from actual tileset graphics</li>
            <li>Map dimensions and player count</li>
            <li>Tileset type detection</li>
            <li>Water analysis and terrain features</li>
          </ul>
        </div>
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
          <button type="submit" class="btn btn-primary">Upload Map</button>
        </div>
      </form>
    `);

    document.getElementById('upload-map-form').addEventListener('submit', async (e) => {
      e.preventDefault();
      try {
        const formData = new FormData(e.target);
        await this.api.post('/admin/maps', formData);
        this.ui.showSuccess('Map uploaded successfully');
        modal.remove();
        await this.loadMapManagement();
      } catch (error) {
        this.ui.showError(`Failed to upload map: ${error.message}`);
      }
    });
  }

  async editMap(mapId) {
    try {
      const map = await this.api.get(`/admin/maps/${mapId}`);
      const modal = this.ui.createModal('edit-map-modal', 'Edit Map', `
        <form id="edit-map-form" class="form">
          <div class="form-group">
            <label for="edit-map-name">Map Name:</label>
            <input type="text" id="edit-map-name" name="name" value="${map.name}" required>
          </div>
          <div class="form-group">
            <label for="edit-map-type">Type:</label>
            <select id="edit-map-type" name="type" required>
              <option value="melee" ${map.type === 'melee' ? 'selected' : ''}>Melee</option>
              <option value="custom" ${map.type === 'custom' ? 'selected' : ''}>Custom</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-map-status">Status:</label>
            <select id="edit-map-status" name="status" required>
              <option value="active" ${map.status === 'active' ? 'selected' : ''}>Active</option>
              <option value="disabled" ${map.status === 'disabled' ? 'selected' : ''}>Disabled</option>
              <option value="pending" ${map.status === 'pending' ? 'selected' : ''}>Pending</option>
            </select>
          </div>
          <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
            <button type="submit" class="btn btn-primary">Update Map</button>
          </div>
        </form>
      `);

      document.getElementById('edit-map-form').addEventListener('submit', async (e) => {
        e.preventDefault();
        try {
          const formData = new FormData(e.target);
          await this.api.put(`/admin/maps/${mapId}`, Object.fromEntries(formData));
          this.ui.showSuccess('Map updated successfully');
          modal.remove();
          await this.loadMapManagement();
        } catch (error) {
          this.ui.showError(`Failed to update map: ${error.message}`);
        }
      });
    } catch (error) {
      this.ui.showError(`Failed to load map details: ${error.message}`);
    }
  }

  async viewMapDetails(mapId) {
    try {
      const map = await this.api.get(`/admin/maps/${mapId}/details`);
      this.ui.createModal('map-details-modal', 'Map Details', `
        <div class="map-details-content">
          <div class="map-preview">
            ${map.thumbnailPath ? 
              `<img src="${map.thumbnailPath}" alt="${map.name}" class="map-image-large">` : 
              `<div class="map-placeholder-large"><i class="fas fa-map"></i></div>`
            }
          </div>
          <div class="map-info-detailed">
            <h3>${map.name}</h3>
            <div class="map-stats-detailed">
              <div class="stat-item">
                <label>Type:</label>
                <span>${map.type}</span>
              </div>
              <div class="stat-item">
                <label>Size:</label>
                <span>${map.size}</span>
              </div>
              <div class="stat-item">
                <label>Game Type:</label>
                <span>${map.gameType}</span>
              </div>
              <div class="stat-item">
                <label>Status:</label>
                <span class="status-badge ${map.status}">${map.status}</span>
              </div>
              <div class="stat-item">
                <label>Play Count:</label>
                <span>${map.playCount}</span>
              </div>
              <div class="stat-item">
                <label>Download Count:</label>
                <span>${map.downloadCount}</span>
              </div>
              <div class="stat-item">
                <label>Average Rating:</label>
                <span>${map.averageRating ? map.averageRating.toFixed(1) : 'N/A'}</span>
              </div>
              <div class="stat-item">
                <label>Uploaded:</label>
                <span>${this.ui.formatDate(map.createdAt)}</span>
              </div>
            </div>
          </div>
        </div>
      `);
    } catch (error) {
      this.ui.showError(`Failed to load map details: ${error.message}`);
    }
  }

  async toggleMapStatus(mapId) {
    try {
      await this.api.put(`/admin/maps/${mapId}/toggle-status`);
      this.ui.showSuccess('Map status updated successfully');
      await this.loadMapManagement();
    } catch (error) {
      this.ui.showError(`Failed to update map status: ${error.message}`);
    }
  }

  async deleteMap(mapId) {
    const confirmed = await this.ui.confirm('Delete Map', 'Are you sure you want to delete this map? This action cannot be undone.');
    if (!confirmed) return;

    try {
      await this.api.delete(`/admin/maps/${mapId}`);
      this.ui.showSuccess('Map deleted successfully');
      await this.loadMapManagement();
    } catch (error) {
      this.ui.showError(`Failed to delete map: ${error.message}`);
    }
  }

  async bulkActivateMaps() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select maps to activate');
      return;
    }

    try {
      await this.api.post('/admin/maps/bulk-activate', { mapIds: Array.from(this.state.selectedItems) });
      this.ui.showSuccess(`${this.state.selectedItems.size} maps activated successfully`);
      this.state.selectedItems.clear();
      await this.loadMapManagement();
    } catch (error) {
      this.ui.showError(`Failed to activate maps: ${error.message}`);
    }
  }

  async bulkDisableMaps() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select maps to disable');
      return;
    }

    try {
      await this.api.post('/admin/maps/bulk-disable', { mapIds: Array.from(this.state.selectedItems) });
      this.ui.showSuccess(`${this.state.selectedItems.size} maps disabled successfully`);
      this.state.selectedItems.clear();
      await this.loadMapManagement();
    } catch (error) {
      this.ui.showError(`Failed to disable maps: ${error.message}`);
    }
  }

  async bulkDeleteMaps() {
    if (this.state.selectedItems.size === 0) {
      this.ui.showError('Please select maps to delete');
      return;
    }

    const confirmed = await this.ui.confirm('Bulk Delete', `Delete ${this.state.selectedItems.size} selected maps? This action cannot be undone.`);
    if (!confirmed) return;

    try {
      await this.api.post('/admin/maps/bulk-delete', { mapIds: Array.from(this.state.selectedItems) });
      this.ui.showSuccess(`${this.state.selectedItems.size} maps deleted successfully`);
      this.state.selectedItems.clear();
      await this.loadMapManagement();
    } catch (error) {
      this.ui.showError(`Failed to delete maps: ${error.message}`);
    }
  }

  // Settings Management Methods
  async saveSettings() {
    try {
      const settings = {};
      
      // Collect all form inputs
      document.querySelectorAll('.settings-container input, .settings-container select, .settings-container textarea').forEach(input => {
        if (input.type === 'checkbox') {
          settings[input.name] = input.checked;
        } else if (input.type === 'number') {
          settings[input.name] = parseInt(input.value);
        } else {
          settings[input.name] = input.value;
        }
      });

      await this.api.put('/admin/settings', settings);
      this.ui.showSuccess('Settings saved successfully');
    } catch (error) {
      this.ui.showError(`Failed to save settings: ${error.message}`);
    }
  }

  async resetSettings() {
    const confirmed = await this.ui.confirm('Reset Settings', 'Are you sure you want to reset all settings to their default values?');
    if (!confirmed) return;

    try {
      await this.api.post('/admin/settings/reset');
      this.ui.showSuccess('Settings reset to defaults');
      await this.loadSystemSettings();
    } catch (error) {
      this.ui.showError(`Failed to reset settings: ${error.message}`);
    }
  }

  // Helper method for flagging screenshots
  async flagScreenshot(url) {
    const confirmed = await this.ui.confirm('Flag Screenshot', 'Are you sure you want to flag this screenshot as inappropriate?');
    if (!confirmed) return;

    try {
      await this.api.post('/admin/screenshots/flag', { url });
      this.ui.showSuccess('Screenshot flagged successfully');
    } catch (error) {
      this.ui.showError(`Failed to flag screenshot: ${error.message}`);
    }
  }

  /**
   * Load tournament management
   */
  async loadTournamentManagement() {
    try {
      const tournaments = await this.api.get('/admin/tournaments');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Tournament Management</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.createTournament()">
                <i class="fas fa-plus"></i> Create Tournament
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportTournaments()">
                <i class="fas fa-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Tournament Filters -->
          <div class="filters-bar">
            <div class="filter-group">
              <label>Status:</label>
              <select id="tournament-status-filter">
                <option value="all">All Status</option>
                <option value="upcoming">Upcoming</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Type:</label>
              <select id="tournament-type-filter">
                <option value="all">All Types</option>
                <option value="single_elimination">Single Elimination</option>
                <option value="double_elimination">Double Elimination</option>
                <option value="round_robin">Round Robin</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Game:</label>
              <select id="tournament-game-filter">
                <option value="all">All Games</option>
                <option value="warcraft2">Warcraft 2</option>
                <option value="warcraft3">Warcraft 3</option>
              </select>
            </div>
          </div>

          <!-- Tournaments Table -->
          <div class="data-table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Tournament</th>
                  <th>Organizer</th>
                  <th>Type</th>
                  <th>Participants</th>
                  <th>Prize Pool</th>
                  <th>Status</th>
                  <th>Start Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                ${tournaments.map(tournament => `
                  <tr>
                    <td>
                      <div class="tournament-info">
                        <div class="tournament-name">${tournament.name}</div>
                        <div class="tournament-id">#${tournament._id.slice(-6)}</div>
                      </div>
                    </td>
                    <td>${tournament.organizer?.username || 'Unknown'}</td>
                    <td><span class="type-badge ${tournament.type}">${tournament.type.replace('_', ' ')}</span></td>
                    <td>${tournament.participants?.length || 0}/${tournament.maxParticipants}</td>
                    <td>${tournament.prizePool ? '$' + tournament.prizePool : 'None'}</td>
                    <td><span class="status-badge ${tournament.status}">${tournament.status}</span></td>
                    <td>${this.ui.formatDate(tournament.startDate)}</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="adminControlPanel.editTournament('${tournament._id}')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="adminControlPanel.viewTournamentDetails('${tournament._id}')">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="adminControlPanel.cancelTournament('${tournament._id}')">
                          <i class="fas fa-ban"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="adminControlPanel.deleteTournament('${tournament._id}')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Failed to load tournament management:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Tournament Management Error', 'Failed to load tournament data.');
    }
  }

  /**
   * Load clan management
   */
  async loadClanManagement() {
    try {
      const clans = await this.api.get('/admin/clans');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Clan Management</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.createClan()">
                <i class="fas fa-plus"></i> Create Clan
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportClans()">
                <i class="fas fa-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Clan Filters -->
          <div class="filters-bar">
            <div class="filter-group">
              <label>Status:</label>
              <select id="clan-status-filter">
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="disbanded">Disbanded</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Size:</label>
              <select id="clan-size-filter">
                <option value="all">All Sizes</option>
                <option value="small">Small (1-10)</option>
                <option value="medium">Medium (11-25)</option>
                <option value="large">Large (26+)</option>
              </select>
            </div>
          </div>

          <!-- Clans Table -->
          <div class="data-table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Clan</th>
                  <th>Leader</th>
                  <th>Members</th>
                  <th>Created</th>
                  <th>Status</th>
                  <th>Rating</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                ${clans.map(clan => `
                  <tr>
                    <td>
                      <div class="clan-info">
                        <div class="clan-name">[${clan.tag}] ${clan.name}</div>
                        <div class="clan-id">#${clan._id.slice(-6)}</div>
                      </div>
                    </td>
                    <td>${clan.leader?.username || 'Unknown'}</td>
                    <td>${clan.members?.length || 0}/${clan.maxMembers || 50}</td>
                    <td>${this.ui.formatDate(clan.createdAt)}</td>
                    <td><span class="status-badge ${clan.status || 'active'}">${clan.status || 'active'}</span></td>
                    <td>${clan.rating || 'Unrated'}</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="adminControlPanel.editClan('${clan._id}')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="adminControlPanel.viewClanDetails('${clan._id}')">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="adminControlPanel.disbandClan('${clan._id}')">
                          <i class="fas fa-ban"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="adminControlPanel.deleteClan('${clan._id}')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Failed to load clan management:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Clan Management Error', 'Failed to load clan data.');
    }
  }

  /**
   * Load achievement management
   */
  async loadAchievementManagement() {
    try {
      const achievements = await this.api.get('/admin/achievements');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Achievement Management</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.createAchievement()">
                <i class="fas fa-plus"></i> Create Achievement
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportAchievements()">
                <i class="fas fa-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Achievement Filters -->
          <div class="filters-bar">
            <div class="filter-group">
              <label>Category:</label>
              <select id="achievement-category-filter">
                <option value="all">All Categories</option>
                <option value="matches">Matches</option>
                <option value="tournaments">Tournaments</option>
                <option value="social">Social</option>
                <option value="special">Special</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Difficulty:</label>
              <select id="achievement-difficulty-filter">
                <option value="all">All Difficulties</option>
                <option value="easy">Easy</option>
                <option value="medium">Medium</option>
                <option value="hard">Hard</option>
                <option value="legendary">Legendary</option>
              </select>
            </div>
          </div>

          <!-- Achievements Table -->
          <div class="data-table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Achievement</th>
                  <th>Category</th>
                  <th>Difficulty</th>
                  <th>Points</th>
                  <th>Unlocked By</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                ${achievements.map(achievement => `
                  <tr>
                    <td>
                      <div class="achievement-info">
                        <div class="achievement-name">${achievement.name}</div>
                        <div class="achievement-description">${achievement.description}</div>
                      </div>
                    </td>
                    <td><span class="category-badge ${achievement.category}">${achievement.category}</span></td>
                    <td><span class="difficulty-badge ${achievement.difficulty}">${achievement.difficulty}</span></td>
                    <td>${achievement.points}</td>
                    <td>${achievement.unlockedBy?.length || 0} users</td>
                    <td>${this.ui.formatDate(achievement.createdAt)}</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="adminControlPanel.editAchievement('${achievement._id}')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="adminControlPanel.viewAchievementDetails('${achievement._id}')">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="adminControlPanel.deleteAchievement('${achievement._id}')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Failed to load achievement management:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Achievement Management Error', 'Failed to load achievement data.');
    }
  }

  /**
   * Load content management
   */
  async loadContentManagement() {
    try {
      const content = await this.api.get('/admin/content');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Content Management</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.createContent()">
                <i class="fas fa-plus"></i> Create Content
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportContent()">
                <i class="fas fa-download"></i> Export
              </button>
            </div>
          </div>

          <!-- Content Filters -->
          <div class="filters-bar">
            <div class="filter-group">
              <label>Type:</label>
              <select id="content-type-filter">
                <option value="all">All Types</option>
                <option value="news">News</option>
                <option value="announcements">Announcements</option>
                <option value="guides">Guides</option>
                <option value="pages">Pages</option>
              </select>
            </div>
            <div class="filter-group">
              <label>Status:</label>
              <select id="content-status-filter">
                <option value="all">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
              </select>
            </div>
          </div>

          <!-- Content Table -->
          <div class="data-table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>Title</th>
                  <th>Type</th>
                  <th>Author</th>
                  <th>Status</th>
                  <th>Views</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                ${content.map(item => `
                  <tr>
                    <td>
                      <div class="content-info">
                        <div class="content-title">${item.title}</div>
                        <div class="content-excerpt">${item.excerpt || 'No excerpt'}</div>
                      </div>
                    </td>
                    <td><span class="type-badge ${item.type}">${item.type}</span></td>
                    <td>${item.author?.username || 'Unknown'}</td>
                    <td><span class="status-badge ${item.status}">${item.status}</span></td>
                    <td>${item.views || 0}</td>
                    <td>${this.ui.formatDate(item.createdAt)}</td>
                    <td>
                      <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="adminControlPanel.editContent('${item._id}')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="adminControlPanel.viewContent('${item._id}')">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="adminControlPanel.publishContent('${item._id}')">
                          <i class="fas fa-globe"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="adminControlPanel.deleteContent('${item._id}')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Failed to load content management:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Content Management Error', 'Failed to load content data.');
    }
  }

  /**
   * Load analytics
   */
  async loadAnalytics() {
    try {
      const analytics = await this.api.get('/admin/analytics');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Analytics & Reports</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.generateReport()">
                <i class="fas fa-chart-line"></i> Generate Report
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportAnalytics()">
                <i class="fas fa-download"></i> Export Data
              </button>
            </div>
          </div>

          <!-- Analytics Dashboard -->
          <div class="analytics-dashboard">
            <div class="analytics-cards">
              <div class="analytics-card">
                <div class="card-header">
                  <h3>User Growth</h3>
                  <i class="fas fa-users"></i>
                </div>
                <div class="card-content">
                  <div class="metric-value">${analytics.userGrowth?.current || 0}</div>
                  <div class="metric-change ${analytics.userGrowth?.change >= 0 ? 'positive' : 'negative'}">
                    ${analytics.userGrowth?.change >= 0 ? '+' : ''}${analytics.userGrowth?.change || 0}%
                  </div>
                </div>
              </div>

              <div class="analytics-card">
                <div class="card-header">
                  <h3>Match Activity</h3>
                  <i class="fas fa-gamepad"></i>
                </div>
                <div class="card-content">
                  <div class="metric-value">${analytics.matchActivity?.current || 0}</div>
                  <div class="metric-change ${analytics.matchActivity?.change >= 0 ? 'positive' : 'negative'}">
                    ${analytics.matchActivity?.change >= 0 ? '+' : ''}${analytics.matchActivity?.change || 0}%
                  </div>
                </div>
              </div>

              <div class="analytics-card">
                <div class="card-header">
                  <h3>Revenue</h3>
                  <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="card-content">
                  <div class="metric-value">$${analytics.revenue?.current || 0}</div>
                  <div class="metric-change ${analytics.revenue?.change >= 0 ? 'positive' : 'negative'}">
                    ${analytics.revenue?.change >= 0 ? '+' : ''}${analytics.revenue?.change || 0}%
                  </div>
                </div>
              </div>

              <div class="analytics-card">
                <div class="card-header">
                  <h3>Engagement</h3>
                  <i class="fas fa-heart"></i>
                </div>
                <div class="card-content">
                  <div class="metric-value">${analytics.engagement?.current || 0}%</div>
                  <div class="metric-change ${analytics.engagement?.change >= 0 ? 'positive' : 'negative'}">
                    ${analytics.engagement?.change >= 0 ? '+' : ''}${analytics.engagement?.change || 0}%
                  </div>
                </div>
              </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
              <div class="chart-container">
                <h3>User Activity Over Time</h3>
                <div id="user-activity-chart" class="chart-placeholder">
                  <p>Chart will be rendered here</p>
                </div>
              </div>

              <div class="chart-container">
                <h3>Match Types Distribution</h3>
                <div id="match-types-chart" class="chart-placeholder">
                  <p>Chart will be rendered here</p>
                </div>
              </div>
            </div>

            <!-- Reports Section -->
            <div class="reports-section">
              <h3>Recent Reports</h3>
              <div class="reports-list">
                ${analytics.recentReports?.map(report => `
                  <div class="report-item">
                    <div class="report-info">
                      <div class="report-name">${report.name}</div>
                      <div class="report-date">${this.ui.formatDate(report.createdAt)}</div>
                    </div>
                    <div class="report-actions">
                      <button class="btn btn-sm btn-primary" onclick="adminControlPanel.viewReport('${report._id}')">
                        <i class="fas fa-eye"></i> View
                      </button>
                      <button class="btn btn-sm btn-secondary" onclick="adminControlPanel.downloadReport('${report._id}')">
                        <i class="fas fa-download"></i> Download
                      </button>
                    </div>
                  </div>
                `).join('') || '<p>No recent reports</p>'}
              </div>
            </div>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Failed to load analytics:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Analytics Error', 'Failed to load analytics data.');
    }
  }

  /**
   * Load moderation tools
   */
  async loadModerationTools() {
    try {
      const moderation = await this.api.get('/admin/moderation');
      
      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1>Moderation Tools</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.createModerationRule()">
                <i class="fas fa-plus"></i> Add Rule
              </button>
              <button class="btn btn-secondary" onclick="adminControlPanel.exportModerationLogs()">
                <i class="fas fa-download"></i> Export Logs
              </button>
            </div>
          </div>

          <!-- Moderation Queue -->
          <div class="moderation-queue">
            <h3>Pending Moderation</h3>
            <div class="queue-stats">
              <div class="queue-stat">
                <span class="stat-label">Reports</span>
                <span class="stat-value">${moderation.pendingReports || 0}</span>
              </div>
              <div class="queue-stat">
                <span class="stat-label">Flagged Content</span>
                <span class="stat-value">${moderation.flaggedContent || 0}</span>
              </div>
              <div class="queue-stat">
                <span class="stat-label">Appeals</span>
                <span class="stat-value">${moderation.appeals || 0}</span>
              </div>
            </div>
          </div>

          <!-- Moderation Actions -->
          <div class="moderation-actions">
            <h3>Quick Actions</h3>
            <div class="action-grid">
              <button class="action-card" onclick="adminControlPanel.reviewReports()">
                <i class="fas fa-flag"></i>
                <span>Review Reports</span>
                <span class="action-count">${moderation.pendingReports || 0}</span>
              </button>

              <button class="action-card" onclick="adminControlPanel.reviewFlaggedContent()">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Flagged Content</span>
                <span class="action-count">${moderation.flaggedContent || 0}</span>
              </button>

              <button class="action-card" onclick="adminControlPanel.reviewAppeals()">
                <i class="fas fa-gavel"></i>
                <span>Appeals</span>
                <span class="action-count">${moderation.appeals || 0}</span>
              </button>

              <button class="action-card" onclick="adminControlPanel.viewBannedUsers()">
                <i class="fas fa-ban"></i>
                <span>Banned Users</span>
                <span class="action-count">${moderation.bannedUsers || 0}</span>
              </button>
            </div>
          </div>

          <!-- Moderation Rules -->
          <div class="moderation-rules">
            <h3>Moderation Rules</h3>
            <div class="rules-list">
              ${moderation.rules?.map(rule => `
                <div class="rule-item">
                  <div class="rule-info">
                    <div class="rule-name">${rule.name}</div>
                    <div class="rule-description">${rule.description}</div>
                  </div>
                  <div class="rule-status">
                    <span class="status-badge ${rule.active ? 'active' : 'inactive'}">
                      ${rule.active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <div class="rule-actions">
                    <button class="btn btn-sm btn-primary" onclick="adminControlPanel.editRule('${rule._id}')">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="adminControlPanel.toggleRule('${rule._id}')">
                      <i class="fas fa-toggle-on"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="adminControlPanel.deleteRule('${rule._id}')">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              `).join('') || '<p>No moderation rules configured</p>'}
            </div>
          </div>

          <!-- Recent Moderation Activity -->
          <div class="moderation-activity">
            <h3>Recent Activity</h3>
            <div class="activity-list">
              ${moderation.recentActivity?.map(activity => `
                <div class="activity-item">
                  <div class="activity-icon">
                    <i class="fas ${this.getModerationIcon(activity.type)}"></i>
                  </div>
                  <div class="activity-content">
                    <div class="activity-description">${activity.description}</div>
                    <div class="activity-meta">
                      <span class="activity-user">${activity.moderator}</span>
                      <span class="activity-time">${this.ui.formatRelativeTime(activity.timestamp)}</span>
                    </div>
                  </div>
                </div>
              `).join('') || '<p>No recent moderation activity</p>'}
            </div>
          </div>
        </div>
      `;

    } catch (error) {
      console.error('Failed to load moderation tools:', error);
      document.getElementById('admin-content').innerHTML = 
        this.ui.createErrorState('Moderation Tools Error', 'Failed to load moderation data.');
    }
  }

  /**
   * Get moderation icon
   */
  getModerationIcon(type) {
    const icons = {
      'user_banned': 'fa-ban',
      'content_removed': 'fa-trash',
      'report_resolved': 'fa-check',
      'appeal_approved': 'fa-gavel',
      'rule_created': 'fa-plus'
    };
    return icons[type] || 'fa-info-circle';
  }

  // Placeholder methods for the new sections
  async createTournament() { this.ui.showInfo('Tournament creation feature coming soon'); }
  async editTournament(id) { this.ui.showInfo('Tournament editing feature coming soon'); }
  async viewTournamentDetails(id) { this.ui.showInfo('Tournament details feature coming soon'); }
  async cancelTournament(id) { this.ui.showInfo('Tournament cancellation feature coming soon'); }
  async deleteTournament(id) { this.ui.showInfo('Tournament deletion feature coming soon'); }
  async exportTournaments() { this.ui.showInfo('Tournament export feature coming soon'); }

  async createClan() { this.ui.showInfo('Clan creation feature coming soon'); }
  async editClan(id) { this.ui.showInfo('Clan editing feature coming soon'); }
  async viewClanDetails(id) { this.ui.showInfo('Clan details feature coming soon'); }
  async disbandClan(id) { this.ui.showInfo('Clan disbanding feature coming soon'); }
  async deleteClan(id) { this.ui.showInfo('Clan deletion feature coming soon'); }
  async exportClans() { this.ui.showInfo('Clan export feature coming soon'); }

  async createAchievement() { this.ui.showInfo('Achievement creation feature coming soon'); }
  async editAchievement(id) { this.ui.showInfo('Achievement editing feature coming soon'); }
  async viewAchievementDetails(id) { this.ui.showInfo('Achievement details feature coming soon'); }
  async deleteAchievement(id) { this.ui.showInfo('Achievement deletion feature coming soon'); }
  async exportAchievements() { this.ui.showInfo('Achievement export feature coming soon'); }

  async createContent() { this.ui.showInfo('Content creation feature coming soon'); }
  async editContent(id) { this.ui.showInfo('Content editing feature coming soon'); }
  async viewContent(id) { this.ui.showInfo('Content viewing feature coming soon'); }
  async publishContent(id) { this.ui.showInfo('Content publishing feature coming soon'); }
  async deleteContent(id) { this.ui.showInfo('Content deletion feature coming soon'); }
  async exportContent() { this.ui.showInfo('Content export feature coming soon'); }

  async generateReport() { this.ui.showInfo('Report generation feature coming soon'); }
  async exportAnalytics() { this.ui.showInfo('Analytics export feature coming soon'); }
  async viewReport(id) { this.ui.showInfo('Report viewing feature coming soon'); }
  async downloadReport(id) { this.ui.showInfo('Report download feature coming soon'); }

  async createModerationRule() { this.ui.showInfo('Moderation rule creation feature coming soon'); }
  async reviewReports() { this.ui.showInfo('Report review feature coming soon'); }
  async reviewFlaggedContent() { this.ui.showInfo('Flagged content review feature coming soon'); }
  async reviewAppeals() { this.ui.showInfo('Appeals review feature coming soon'); }
  async viewBannedUsers() { this.ui.showInfo('Banned users view feature coming soon'); }
  async editRule(id) { this.ui.showInfo('Rule editing feature coming soon'); }
  async toggleRule(id) { this.ui.showInfo('Rule toggle feature coming soon'); }
  async deleteRule(id) { this.ui.showInfo('Rule deletion feature coming soon'); }
  async exportModerationLogs() { this.ui.showInfo('Moderation logs export feature coming soon'); }

  /**
   * Get mock dashboard stats when API is not available
   */
  getMockDashboardStats() {
    return {
      users: {
        total: 1247,
        change: 12,
        active: 89,
        new: 23
      },
      matches: {
        total: 3456,
        change: 8,
        pending: 12,
        disputed: 3
      },
      tournaments: {
        total: 45,
        change: 5,
        active: 3,
        upcoming: 7
      },
      clans: {
        total: 89,
        change: -2,
        active: 67,
        recruiting: 23
      },
      system: {
        uptime: '99.8%',
        load: '23%',
        storage: '67%',
        memory: '45%'
      },
      recentActivity: [
        {
          type: 'user_registered',
          message: 'New user "PlayerX" registered',
          timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
          severity: 'info'
        },
        {
          type: 'match_disputed',
          message: 'Match #1234 disputed by PlayerY',
          timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
          severity: 'warning'
        },
        {
          type: 'tournament_created',
          message: 'Tournament "Weekly Cup #45" created',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          severity: 'success'
        },
        {
          type: 'system_alert',
          message: 'High server load detected',
          timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
          severity: 'error'
        }
      ]
    };
  }

  /**
   * Get mock users when API is not available
   */
  getMockUsers() {
    return [
      {
        _id: '507f1f77bcf86cd799439011',
        username: 'AdminUser',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        avatar: '/assets/img/default-avatar.svg',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
        lastActive: new Date(Date.now() - 1000 * 60 * 15).toISOString()
      },
      {
        _id: '507f1f77bcf86cd799439012',
        username: 'ModeratorUser',
        email: '<EMAIL>',
        role: 'moderator',
        status: 'active',
        avatar: '/assets/img/default-avatar.svg',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20).toISOString(),
        lastActive: new Date(Date.now() - 1000 * 60 * 30).toISOString()
      },
      {
        _id: '507f1f77bcf86cd799439013',
        username: 'PlayerOne',
        email: '<EMAIL>',
        role: 'user',
        status: 'active',
        avatar: '/assets/img/default-avatar.svg',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10).toISOString(),
        lastActive: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString()
      },
      {
        _id: '507f1f77bcf86cd799439014',
        username: 'PlayerTwo',
        email: '<EMAIL>',
        role: 'user',
        status: 'suspended',
        avatar: '/assets/img/default-avatar.svg',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
        lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
      },
      {
        _id: '507f1f77bcf86cd799439015',
        username: 'BannedPlayer',
        email: '<EMAIL>',
        role: 'user',
        status: 'banned',
        avatar: '/assets/img/default-avatar.svg',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(),
        lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString()
      }
    ];
  }

  /**
   * Create database backup
   */
  async createDatabaseBackup() {
    try {
      this.ui.showLoading('Creating database backup...');
      
      const response = await this.api.post('/admin/backup/create');
      
      if (response.success) {
        this.ui.showSuccess('Database backup created successfully!');
        await this.refreshBackupList();
      } else {
        throw new Error(response.error || 'Backup creation failed');
      }
      
    } catch (error) {
      console.error('Error creating backup:', error);
      this.ui.showError(`Failed to create backup: ${error.message}`);
    } finally {
      this.ui.hideLoading();
    }
  }

  /**
   * Refresh backup list
   */
  async refreshBackupList() {
    try {
      const backupsData = await this.api.get('/admin/backup/list');
      const backupListContainer = document.getElementById('backup-list');
      
      if (backupListContainer) {
        backupListContainer.innerHTML = `
          <h4>Existing Backups</h4>
          ${this.renderBackupList(backupsData.backups)}
        `;
      }
      
    } catch (error) {
      console.error('Error refreshing backup list:', error);
      this.ui.showError('Failed to refresh backup list');
    }
  }

  /**
   * Render backup list
   */
  renderBackupList(backups) {
    if (!backups || backups.length === 0) {
      return `
        <div class="no-backups">
          <i class="fas fa-database"></i>
          <p>No backups found</p>
          <small>Create your first backup to get started</small>
        </div>
      `;
    }

    return `
      <div class="backup-table">
        <table class="data-table">
          <thead>
            <tr>
              <th>Backup Name</th>
              <th>Created</th>
              <th>Size</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${backups.map(backup => `
              <tr>
                <td>
                  <div class="backup-info">
                    <i class="fas fa-database"></i>
                    <span>${backup.name}</span>
                  </div>
                </td>
                <td>${new Date(backup.created).toLocaleDateString()} ${new Date(backup.created).toLocaleTimeString()}</td>
                <td>${backup.size}</td>
                <td>
                  <div class="action-buttons">
                    <button class="btn btn-sm btn-danger" onclick="adminControlPanel.deleteBackup('${backup.name}')" title="Delete Backup">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  /**
   * Delete backup
   */
  async deleteBackup(backupName) {
    try {
      const confirmDelete = await this.ui.confirm(
        'Delete Backup',
        `Are you sure you want to delete the backup "${backupName}"? This action cannot be undone.`
      );
      
      if (!confirmDelete) return;
      
      this.ui.showLoading('Deleting backup...');
      
      const response = await this.api.delete(`/admin/backup/${backupName}`);
      
      if (response.success) {
        this.ui.showSuccess('Backup deleted successfully!');
        await this.refreshBackupList();
      } else {
        throw new Error(response.error || 'Backup deletion failed');
      }
      
    } catch (error) {
      console.error('Error deleting backup:', error);
      this.ui.showError(`Failed to delete backup: ${error.message}`);
    } finally {
      this.ui.hideLoading();
    }
  }

  /**
   * Get mock settings for fallback
   */
  getMockSettings() {
    return {
      general: {
        siteName: 'Warcraft Arena',
        siteDescription: 'The premier Warcraft gaming platform',
        maintenanceMode: false,
        registrationEnabled: true
      },
      matches: {
        autoVerify: false,
        editWindow: 24,
        requiredScreenshots: 1,
        maxFileSize: 10
      },
      tournaments: {
        maxPerUser: 3,
        creationCost: 1000,
        autoStart: false
      },
      clans: {
        creationCost: 500,
        maxMembers: 50,
        maxTagLength: 4
      },
      achievements: {
        notifications: true,
        retroactive: false,
        baseExperience: 100
      }
    };
  }

  /**
   * Check if admin is currently impersonating a user
   */
  async checkImpersonationStatus() {
    try {
      const response = await this.api.get('/admin/impersonation-status');
      if (response && response.isImpersonating) {
        this.showImpersonationBanner(response.impersonatedUser, response.originalAdmin);
      }
    } catch (error) {
      // Silently fail if endpoint doesn't exist - this is optional functionality
      console.log('Impersonation status check skipped (endpoint not available)');
    }
  }

  /**
   * Show impersonation banner
   */
  showImpersonationBanner(impersonatedUser, originalAdmin) {
    // Remove existing banner
    const existingBanner = document.querySelector('.impersonation-banner');
    if (existingBanner) {
      existingBanner.remove();
    }

    // Create impersonation banner
    const banner = document.createElement('div');
    banner.className = 'impersonation-banner';
    banner.innerHTML = `
      <div class="impersonation-content">
        <div class="impersonation-info">
          <i class="fas fa-user-secret"></i>
          <span>Impersonating: <strong>${impersonatedUser.username}</strong></span>
          <span class="original-admin">Admin: ${originalAdmin.username}</span>
        </div>
        <div class="impersonation-actions">
          <button class="btn btn-sm btn-warning" onclick="adminControlPanel.stopImpersonation()">
            <i class="fas fa-sign-out-alt"></i> Stop Impersonation
          </button>
          <button class="btn btn-sm btn-primary" onclick="adminControlPanel.showUserSwitcher()">
            <i class="fas fa-exchange-alt"></i> Switch User
          </button>
        </div>
      </div>
    `;

    // Insert banner at top of page
    document.body.insertBefore(banner, document.body.firstChild);
  }

  /**
   * Show user switcher modal
   */
  async showUserSwitcher() {
    try {
      const response = await this.api.get('/admin/users-for-impersonation');
      const users = response.users;

      const modal = this.ui.createModal('user-switcher-modal', 'Switch User Account', `
        <div class="user-switcher-content">
          <div class="switcher-header">
            <h3>Select User to Impersonate</h3>
            <p>Choose a user account to impersonate. You can switch back to your admin account at any time.</p>
          </div>
          
          <div class="user-search">
            <input type="text" id="user-search-input" placeholder="Search users..." class="form-control">
          </div>
          
          <div class="users-list" id="users-list">
            ${users.map(user => `
              <div class="user-item" data-user-id="${user._id}">
                <div class="user-info">
                  <div class="user-name">${user.username}</div>
                  <div class="user-email">${user.email}</div>
                  <div class="user-meta">
                    Joined: ${new Date(user.createdAt).toLocaleDateString()}
                    ${user.lastActive ? ` | Last active: ${new Date(user.lastActive).toLocaleDateString()}` : ''}
                  </div>
                </div>
                <div class="user-actions">
                  <button class="btn btn-primary btn-sm" onclick="adminControlPanel.impersonateUser('${user._id}', '${user.username}')">
                    <i class="fas fa-user-secret"></i> Impersonate
                  </button>
                </div>
              </div>
            `).join('')}
          </div>
          
          <div class="modal-footer">
            <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancel</button>
          </div>
        </div>
      `);

      // Add search functionality
      const searchInput = document.getElementById('user-search-input');
      const usersList = document.getElementById('users-list');
      const allUserItems = usersList.querySelectorAll('.user-item');

      searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();
        allUserItems.forEach(item => {
          const userName = item.querySelector('.user-name').textContent.toLowerCase();
          const userEmail = item.querySelector('.user-email').textContent.toLowerCase();
          const matches = userName.includes(query) || userEmail.includes(query);
          item.style.display = matches ? 'flex' : 'none';
        });
      });

    } catch (error) {
      console.error('Error loading user switcher:', error);
      this.ui.showError('Failed to load user list');
    }
  }

  /**
   * Impersonate a user
   */
  async impersonateUser(userId, username) {
    try {
      this.ui.showLoading(`Switching to ${username}...`);
      
      const response = await this.api.post(`/admin/impersonate/${userId}`);
      
      if (response.success) {
        // Close modal
        document.querySelector('.modal')?.remove();
        
        // Show success message
        this.ui.showSuccess(`Now impersonating ${username}`);
        
        // Show impersonation banner
        this.showImpersonationBanner(response.impersonatedUser, response.originalAdmin);
        
        // Reload page to reflect new user context
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        
      } else {
        throw new Error(response.error || 'Failed to impersonate user');
      }
      
    } catch (error) {
      console.error('Error impersonating user:', error);
      this.ui.showError(`Failed to impersonate user: ${error.message}`);
    } finally {
      this.ui.hideLoading();
    }
  }

  /**
   * Stop impersonation and return to admin account
   */
  async stopImpersonation() {
    try {
      this.ui.showLoading('Returning to admin account...');
      
      const response = await this.api.post('/admin/stop-impersonation');
      
      if (response.success) {
        // Remove impersonation banner
        const banner = document.querySelector('.impersonation-banner');
        if (banner) {
          banner.remove();
        }
        
        // Show success message
        this.ui.showSuccess('Returned to admin account');
        
        // Reload page to reflect admin context
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        
      } else {
        throw new Error(response.error || 'Failed to stop impersonation');
      }
      
    } catch (error) {
      console.error('Error stopping impersonation:', error);
      this.ui.showError(`Failed to stop impersonation: ${error.message}`);
    } finally {
      this.ui.hideLoading();
    }
  }

  /**
   * Load image cache management
   */
  async loadImageCacheManagement() {
    try {
      console.log('Loading image cache management...');

      const contentContainer = document.getElementById('admin-content');
      contentContainer.innerHTML = `
        <div class="admin-section">
          <div class="section-header">
            <h1><i class="fas fa-images"></i> Image Cache Management</h1>
            <div class="section-actions">
              <button class="btn btn-primary" onclick="adminControlPanel.refreshImageCacheStats()">
                <i class="fas fa-sync"></i> Refresh
              </button>
            </div>
          </div>

          <!-- Cache Statistics -->
          <div class="cache-stats-section">
            <div class="cache-stats-card">
              <h3><i class="fas fa-chart-bar"></i> Cache Statistics</h3>
              <div id="image-cache-stats-content">
                <div class="text-center">
                  <i class="fas fa-spinner fa-spin fa-2x"></i>
                  <p>Loading cache statistics...</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Management Actions -->
          <div class="row">
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5><i class="fas fa-tools"></i> Cache Management</h5>
                </div>
                <div class="card-body">
                  <div class="d-grid gap-2">
                    <button class="btn btn-warning" onclick="adminControlPanel.clearPlatformImageCache('youtube')">
                      <i class="fab fa-youtube"></i> Clear YouTube Cache
                    </button>
                    <button class="btn btn-warning" onclick="adminControlPanel.clearPlatformImageCache('twitch')">
                      <i class="fab fa-twitch"></i> Clear Twitch Cache
                    </button>
                    <button class="btn btn-info" onclick="adminControlPanel.refreshAllProfileImages()">
                      <i class="fas fa-images"></i> Refresh All Images
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5><i class="fas fa-exclamation-triangle"></i> Error Monitoring</h5>
                </div>
                <div class="card-body">
                  <div id="image-error-monitoring">
                    <p class="text-muted">Error information will be displayed here</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Activity Log -->
          <div class="row mt-4">
            <div class="col-12">
              <div class="card">
                <div class="card-header">
                  <h5><i class="fas fa-list"></i> Activity Log</h5>
                </div>
                <div class="card-body">
                  <div id="image-cache-activity-log" style="max-height: 400px; overflow-y: auto;">
                    <!-- Activity entries will be added here -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;

      // Initialize image cache management
      this.initializeImageCacheManagement();

      // Load initial stats
      await this.refreshImageCacheStats();

    } catch (error) {
      console.error('Failed to load image cache management:', error);
      document.getElementById('admin-content').innerHTML =
        this.ui.createErrorState('Image Cache Error', 'Failed to load image cache management.');
    }
  }

  /**
   * Initialize image cache management functionality
   */
  initializeImageCacheManagement() {
    this.imageCacheActivityLog = [];

    // Auto-refresh every 30 seconds
    if (this.imageCacheRefreshInterval) {
      clearInterval(this.imageCacheRefreshInterval);
    }
    this.imageCacheRefreshInterval = setInterval(() => {
      if (this.state.currentSection === 'image-cache') {
        this.refreshImageCacheStats();
      }
    }, 30000);
  }

  /**
   * Add activity to image cache log
   */
  addImageCacheActivity(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const icon = type === 'error' ? 'fa-exclamation-circle' :
                type === 'success' ? 'fa-check-circle' : 'fa-info-circle';
    const colorClass = type === 'error' ? 'text-danger' :
                      type === 'success' ? 'text-success' : 'text-info';

    this.imageCacheActivityLog.unshift({
      timestamp,
      message,
      type,
      icon,
      colorClass
    });

    // Keep only last 50 entries
    if (this.imageCacheActivityLog.length > 50) {
      this.imageCacheActivityLog = this.imageCacheActivityLog.slice(0, 50);
    }

    this.updateImageCacheActivityDisplay();
  }

  /**
   * Update image cache activity display
   */
  updateImageCacheActivityDisplay() {
    const logContainer = document.getElementById('image-cache-activity-log');
    if (!logContainer) return;

    logContainer.innerHTML = this.imageCacheActivityLog.map(entry => `
      <div class="d-flex align-items-center mb-2">
        <i class="fas ${entry.icon} ${entry.colorClass} me-2"></i>
        <span class="text-muted me-2">${entry.timestamp}</span>
        <span>${entry.message}</span>
      </div>
    `).join('');
  }

  /**
   * Refresh image cache statistics
   */
  async refreshImageCacheStats() {
    try {
      this.addImageCacheActivity('Refreshing cache statistics...');

      const response = await this.api.get('/channels/cache-stats');
      this.displayImageCacheStats(response);
      this.addImageCacheActivity('Cache statistics refreshed successfully', 'success');

    } catch (error) {
      console.error('Error refreshing image cache stats:', error);
      this.addImageCacheActivity(`Error refreshing cache stats: ${error.message}`, 'error');

      const statsContainer = document.getElementById('image-cache-stats-content');
      if (statsContainer) {
        statsContainer.innerHTML = `
          <div class="text-center text-danger">
            <i class="fas fa-exclamation-triangle fa-2x"></i>
            <p>Error loading cache statistics</p>
            <small>${error.message}</small>
          </div>
        `;
      }
    }
  }

  /**
   * Display image cache statistics
   */
  displayImageCacheStats(data) {
    const { cacheStats, userStats } = data;

    let content = `
      <div class="row">
        <div class="col-md-6">
          <div class="stat-item">
            <h6><i class="fas fa-users"></i> User Statistics</h6>
            <p><strong>Total Users:</strong> ${userStats.total}</p>
            <p><strong>With YouTube:</strong> ${userStats.withYoutube}</p>
            <p><strong>With Twitch:</strong> ${userStats.withTwitch}</p>
            <p><strong>With Errors:</strong> <span class="text-danger">${userStats.withErrors}</span></p>
          </div>
        </div>
        <div class="col-md-6">
          <div class="stat-item">
            <h6><i class="fas fa-database"></i> Cache Statistics</h6>
    `;

    if (cacheStats && cacheStats.length > 0) {
      cacheStats.forEach(stat => {
        const platformClass = stat._id === 'youtube' ? 'badge bg-danger' : 'badge bg-primary';
        const avgAgeHours = Math.round(stat.avgAge / (1000 * 60 * 60));

        content += `
          <div class="mb-2">
            <span class="${platformClass}">${stat._id.toUpperCase()}</span>
            <span class="ms-2">Cached: ${stat.count} | Errors: ${stat.errors} | Avg Age: ${avgAgeHours}h</span>
          </div>
        `;
      });
    } else {
      content += '<p class="text-muted">No cache data available</p>';
    }

    content += `
          </div>
        </div>
      </div>
    `;

    const statsContainer = document.getElementById('image-cache-stats-content');
    if (statsContainer) {
      statsContainer.innerHTML = content;
    }
  }

  /**
   * Clear platform cache
   */
  async clearPlatformImageCache(platform) {
    if (!confirm(`Are you sure you want to clear all ${platform} cache entries?`)) {
      return;
    }

    try {
      this.addImageCacheActivity(`Clearing ${platform} cache...`);

      const response = await this.api.delete(`/channels/cache/${platform}`);
      this.addImageCacheActivity(`${response.message} (${response.clearedEntries} entries)`, 'success');

      // Refresh stats after clearing
      setTimeout(() => this.refreshImageCacheStats(), 1000);

    } catch (error) {
      console.error('Error clearing cache:', error);
      this.addImageCacheActivity(`Error clearing ${platform} cache: ${error.message}`, 'error');
    }
  }

  /**
   * Refresh all profile images
   */
  async refreshAllProfileImages() {
    if (!confirm('This will refresh profile images for all content creators. Continue?')) {
      return;
    }

    try {
      this.addImageCacheActivity('Starting bulk image refresh...');

      const response = await this.api.post('/channels/refresh-all-images', {
        batchSize: 5,
        force: false
      });

      this.addImageCacheActivity(`Bulk refresh completed: ${response.summary.updated} updated, ${response.summary.errors} errors`, 'success');

      // Refresh stats after bulk update
      setTimeout(() => this.refreshImageCacheStats(), 2000);

    } catch (error) {
      console.error('Error refreshing all images:', error);
      this.addImageCacheActivity(`Error during bulk refresh: ${error.message}`, 'error');
    }
  }
}

// Create global instance
window.adminControlPanel = new AdminControlPanel();