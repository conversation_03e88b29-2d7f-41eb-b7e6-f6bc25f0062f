/**
 * Modern API Client
 * Handles all API communications with consistent error handling
 */

class ApiClient {
  constructor(baseURL = '') {
    this.baseURL = baseURL;
  }

  /**
   * Make HTTP request with error handling
   */
  async request(endpoint, options = {}) {
    try {
      const url = `${this.baseURL}${endpoint}`;

      const config = {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      };

      const response = await fetch(url, config);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          // Clone the response so we can read it multiple times if needed
          const responseClone = response.clone();
          const errorData = await responseClone.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch (e) {
          // If response is not JSON, try to get text from original response
          try {
            const errorText = await response.text();
            if (errorText && errorText.length < 200) {
              errorMessage = errorText;
            }
          } catch (e2) {
            // Use default error message
          }
        }
        throw new Error(errorMessage);
      }

      // Parse JSON response
      const jsonResponse = await response.json();
      
      // If the response already has a success field, return it directly
      if (typeof jsonResponse === 'object' && 'success' in jsonResponse) {
        return jsonResponse;
      }
      
      // Otherwise wrap it for consistency
      return {
        success: true,
        data: jsonResponse
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}) {
    const url = new URL(`${this.baseURL}${endpoint}`, window.location.origin);
    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
    
    return this.request(url.pathname + url.search);
  }

  /**
   * POST request
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * PUT request
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  /**
   * DELETE request
   */
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    });
  }

  /**
   * Upload file with FormData
   */
  async uploadFile(endpoint, formData) {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const response = await fetch(url, {
        method: 'POST',
        credentials: 'include',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Upload failed' }));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const jsonResponse = await response.json();
      
      // If the response already has a success field, return it directly
      if (typeof jsonResponse === 'object' && 'success' in jsonResponse) {
        return jsonResponse;
      }
      
      // Otherwise wrap it for consistency
      return {
        success: true,
        data: jsonResponse
      };
    } catch (error) {
      console.error('File upload failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // === USER ENDPOINTS ===

  /**
   * Get current user data
   */
  async getCurrentUser() {
    try {
      const response = await fetch('/api/me', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const userData = await response.json();
      console.log('ApiClient.getCurrentUser() response:', userData);
      return userData; // Return user data directly, not wrapped
    } catch (error) {
      console.error('getCurrentUser failed:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData) {
    return this.put('/me', profileData);
  }

  /**
   * Update username
   */
  async updateUsername(newUsername) {
    return this.patch('/me/username', { username: newUsername });
  }

  /**
   * Update social links
   */
  async updateSocialLinks(socialLinks) {
    return this.patch('/me/social-links', { socialLinks });
  }

  /**
   * Update game preference
   */
  async updateGamePreference(platform, gameType, isEnabled) {
    return this.patch('/me/content-creator', {
      platform,
      gameType,
      isEnabled
    });
  }

  // === PLAYER ENDPOINTS ===

  /**
   * Get user's players
   */
  async getUserPlayers() {
    return this.get('/api/ladder/my-players');
  }

  /**
   * Add new player
   */
  async addPlayer(playerData) {
    return this.post('/api/ladder/players', playerData);
  }

  /**
   * Update player
   */
  async updatePlayer(playerId, playerData) {
    return this.put(`/players/${playerId}`, playerData);
  }

  /**
   * Remove player
   */
  async removePlayer(playerId) {
    return this.delete(`/players/${playerId}`);
  }

  /**
   * Get player stats
   */
  async getPlayerStats(playerId) {
    return this.get(`/players/${playerId}/stats`);
  }

  // === TOURNAMENT ENDPOINTS ===

  /**
   * Get user's tournaments
   */
  async getUserTournaments() {
    return this.get('/tournaments/user');
  }

  /**
   * Get tournament details
   */
  async getTournament(tournamentId) {
    return this.get(`/tournaments/${tournamentId}`);
  }

  /**
   * Create tournament
   */
  async createTournament(tournamentData) {
    return this.post('/tournaments', tournamentData);
  }

  /**
   * Update tournament
   */
  async updateTournament(tournamentId, tournamentData) {
    return this.put(`/tournaments/${tournamentId}`, tournamentData);
  }

  /**
   * Join tournament
   */
  async joinTournament(tournamentId, playerData) {
    return this.post(`/tournaments/${tournamentId}/register`, playerData);
  }

  /**
   * Leave tournament
   */
  async leaveTournament(tournamentId) {
    return this.post(`/tournaments/${tournamentId}/leave`);
  }

  // === LADDER ENDPOINTS ===

  /**
   * Get ladder rankings
   */
  async getLadderRankings(params = {}) {
    return this.get('/ladder/rankings', params);
  }

  /**
   * Get ladder ranks
   */
  async getLadderRanks() {
    return this.get('/ladder/ranks');
  }

  /**
   * Submit match report
   */
  async submitMatchReport(matchData) {
    return this.post('/ladder/report', matchData);
  }

  /**
   * Get recent matches
   */
  async getRecentMatches(params = {}) {
    return this.get('/ladder/matches', params);
  }

  /**
   * Dispute match
   */
  async disputeMatch(matchId, disputeData) {
    return this.post('/ladder/dispute-match', { matchId, ...disputeData });
  }

  /**
   * Get global stats
   */
  async getGlobalStats() {
    return this.get('/ladder/global-stats');
  }

  // === MAP ENDPOINTS ===

  /**
   * Get maps
   */
  async getMaps(params = {}) {
    return this.get('/maps', params);
  }

  /**
   * Search maps
   */
  async searchMaps(query) {
    return this.get('/maps/search', { q: query });
  }

  /**
   * Get user's maps
   */
  async getUserMaps() {
    return this.get('/maps/user');
  }

  /**
   * Upload map
   */
  async uploadMap(mapData) {
    return this.uploadFile('/api/war2maps/upload', mapData);
  }

  // === ACTIVITY ENDPOINTS ===

  /**
   * Get user activity
   */
  async getUserActivity() {
    return this.get('/activity/user');
  }

  /**
   * Get user reports
   */
  async getUserReports() {
    return this.get('/reports/user');
  }

  // === NOTIFICATION ENDPOINTS ===

  /**
   * Get notifications
   */
  async getNotifications() {
    return this.get('/notifications');
  }

  /**
   * Mark notification as read
   */
  async markNotificationRead(notificationId) {
    return this.patch(`/notifications/${notificationId}/read`);
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsRead() {
    return this.patch('/notifications/read-all');
  }

  // === CHAT ENDPOINTS ===

  /**
   * Get chat messages
   */
  async getChatMessages(params = {}) {
    return this.get('/chat/messages', params);
  }

  /**
   * Send chat message
   */
  async sendChatMessage(messageData) {
    return this.post('/chat/messages', messageData);
  }

  // === ADMIN ENDPOINTS ===

  /**
   * Get admin stats
   */
  async getAdminStats() {
    return this.get('/admin/stats');
  }

  /**
   * Get admin users
   */
  async getAdminUsers(params = {}) {
    return this.get('/admin/users', params);
  }

  /**
   * Update user admin
   */
  async updateUserAdmin(userId, userData) {
    return this.put(`/admin/users/${userId}`, userData);
  }

  // Ladder specific methods
  async getMyPlayers() {
    return this.get('/api/ladder/my-players');
  }

  

  async selectGameType(gameType) {
    return this.post('/api/user/game-type', { gameType });
  }

  async createPlayer(playerData) {
    return this.post('/api/ladder/players', playerData);
  }

  async getMapsByType(gameType) {
    return this.get('/api/war2maps', { gameType });
  }

  async reportMatch(matchData) {
    return this.post('/api/ladder/matches', matchData);
  }
}

// Export the class
export { ApiClient };

// Create and export a default instance
export default new ApiClient();

// Make it globally available
window.ApiClient = ApiClient; 