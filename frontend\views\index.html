<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- CSP temporarily disabled for Square payment testing -->
  <!-- <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://sandbox.web.squarecdn.com https://web.squarecdn.com https://pay.google.com https://js-sandbox.squarecdn.com https://js.squarecdn.com https://js.afterpay.com https://js-sandbox.afterpay.com https://o160250.ingest.sentry.io; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com https://sandbox.web.squarecdn.com https://web.squarecdn.com; font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com https://square-fonts-production-f.squarecdn.com https://d1g145x70srn7h.cloudfront.net; img-src 'self' data: https:; connect-src 'self' https://*.googleapis.com https://*.google.com https://connect.squareupsandbox.com https://connect.squareup.com https://pci-connect.squareupsandbox.com https://pci-connect.squareup.com https://pay.google.com https://google.com/pay https://*.afterpay.com https://*.clearpay.co.uk https://o160250.ingest.sentry.io https://*.ingest.sentry.io; frame-src 'self' https://www.youtube.com https://sandbox.web.squarecdn.com https://web.squarecdn.com https://pay.google.com https://*.afterpay.com https://*.clearpay.co.uk;"> -->
  <title>WC Arena - Epic PvP Battles Await</title>
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Enhanced Index Styling -->
  <style>
    /* ===== EPIC HERO SECTION ===== */
    .epic-hero {
      position: relative;
      min-height: 100vh;
      background: linear-gradient(135deg, 
        #0a0a0a 0%,
        #1a1a2e 25%,
        #16213e 50%,
        #0f3460 75%,
        #1a1a2e 100%
      );
      background-size: 400% 400%;
      animation: gradientShift 8s ease infinite;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* Animated background particles */
    .epic-hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255, 215, 0, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 215, 0, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.1), transparent);
      background-repeat: repeat;
      background-size: 200px 100px;
      animation: sparkle 3s linear infinite;
      pointer-events: none;
    }

    @keyframes sparkle {
      0% { transform: translateY(0) scale(1); opacity: 1; }
      100% { transform: translateY(-100px) scale(0.8); opacity: 0; }
    }

    .hero-content {
      position: relative;
      z-index: 2;
      max-width: 1000px;
      padding: 2rem;
    }

    .hero-title {
      font-family: 'Cinzel', serif;
      font-size: clamp(3rem, 8vw, 7rem);
      font-weight: 800;
      background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700, #ff6b35);
      background-size: 300% 300%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: goldShimmer 4s ease-in-out infinite;
      margin-bottom: 1.5rem;
      text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
      letter-spacing: 2px;
    }

    @keyframes goldShimmer {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    .hero-subtitle {
      font-family: 'Inter', sans-serif;
      font-size: clamp(1.2rem, 3vw, 2rem);
      font-weight: 300;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 3rem;
      line-height: 1.6;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    }

    .hero-cta-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      align-items: center;
    }

    .epic-btn {
      position: relative;
      display: inline-flex;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem 3rem;
      font-family: 'Cinzel', serif;
      font-size: 1.3rem;
      font-weight: 600;
      text-decoration: none;
      color: #000;
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      border: none;
      border-radius: 50px;
      box-shadow: 
        0 8px 25px rgba(255, 215, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      overflow: hidden;
      cursor: pointer;
    }

    .epic-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.5s;
    }

    .epic-btn:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 
        0 15px 35px rgba(255, 215, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .epic-btn:hover::before {
      left: 100%;
    }

    .epic-btn:active {
      transform: translateY(-1px) scale(1.02);
    }

    .epic-btn i {
      font-size: 1.5rem;
    }

    /* ===== VERSION INFO ===== */
    .version-info {
      text-align: center;
      padding: 2rem 0;
      background: rgba(0, 0, 0, 0.3);
      border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    }

    .version-title {
      font-family: 'Cinzel', serif;
      font-size: 2rem;
      color: #ffd700;
      margin-bottom: 1rem;
    }

    .version-subtitle {
      font-family: 'Inter', sans-serif;
      font-size: 1.2rem;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 2rem;
    }

    /* ===== SECTIONS WITH GLASS MORPHISM ===== */
    .glass-section {
      position: relative;
      margin: 4rem auto;
      max-width: 1200px;
      padding: 3rem;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .glass-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.5), transparent);
    }

    .section-title {
      font-family: 'Cinzel', serif;
      font-size: 2.5rem;
      font-weight: 700;
      color: #ffd700;
      text-align: center;
      margin-bottom: 2rem;
      text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
    }

    .section-subtitle {
      font-family: 'Inter', sans-serif;
      font-size: 1.1rem;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      margin-bottom: 3rem;
      line-height: 1.6;
    }

    /* ===== COLLAPSIBLE SECTIONS ===== */
    .feature-sections {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      margin-bottom: 3rem;
    }

    .collapsible-section {
      background: rgba(255, 255, 255, 0.03);
      border: 1px solid rgba(255, 215, 0, 0.2);
      border-radius: 15px;
      overflow: hidden;
    }

    .collapsible-header {
      padding: 1.5rem;
      background: rgba(255, 215, 0, 0.1);
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: 'Cinzel', serif;
      font-size: 1.3rem;
      color: #ffd700;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .collapsible-header:hover {
      background: rgba(255, 215, 0, 0.15);
    }

    .collapsible-content {
      padding: 0 1.5rem;
      max-height: 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .collapsible-content.expanded {
      padding: 1.5rem;
      max-height: 500px;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .feature-list li {
      padding: 0.5rem 0;
      color: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .feature-list li i {
      color: #ffd700;
      width: 16px;
    }

    /* ===== MEMBERSHIP STYLES ===== */
    .membership-showcase {
      max-width: 1000px;
      margin: 0 auto;
    }

    .membership-tiers {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .membership-tier {
      position: relative;
      padding: 2rem 1.5rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      text-align: center;
      transition: all 0.3s ease;
    }

    .membership-tier:hover {
      transform: translateY(-10px);
      background: rgba(255, 215, 0, 0.1);
      border-color: rgba(255, 215, 0, 0.3);
      box-shadow: 0 20px 40px rgba(255, 215, 0, 0.2);
    }

    .membership-tier.featured {
      border-color: rgba(255, 215, 0, 0.5);
      background: rgba(255, 215, 0, 0.08);
      transform: scale(1.05);
    }

    .membership-tier.featured::before {
      content: '✨ MOST POPULAR ✨';
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      color: #000;
      padding: 0.5rem 1rem;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: 700;
    }

    .tier-name {
      font-family: 'Cinzel', serif;
      font-size: 1.5rem;
      color: #ffd700;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .tier-price {
      font-family: 'Cinzel', serif;
      font-size: 2.5rem;
      color: #fff;
      margin-bottom: 1.5rem;
      font-weight: 700;
    }

    .currency {
      font-size: 1.5rem;
      color: #ffd700;
    }

    .tier-description {
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 2rem;
      line-height: 1.5;
    }

    .tier-images {
      display: flex;
      justify-content: center;
      gap: 0.5rem;
      margin-bottom: 2rem;
    }

    .tier-image {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid rgba(255, 215, 0, 0.3);
    }

    .tier-buttons {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .tier-btn {
      padding: 1rem 1.5rem;
      border: none;
      border-radius: 10px;
      font-family: 'Inter', sans-serif;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .tier-btn.monthly {
      background: linear-gradient(45deg, #0070f3, #0051cc);
      color: white;
    }

    .tier-btn.monthly:hover {
      background: linear-gradient(45deg, #0051cc, #003d99);
      transform: translateY(-2px);
    }

    .tier-btn:disabled {
      background: rgba(0, 255, 0, 0.2);
      color: #00ff00;
      cursor: not-allowed;
    }

    .current-tier-badge {
      background: linear-gradient(45deg, #00ff00, #00cc00);
      color: #000;
      padding: 0.3rem 1rem;
      border-radius: 15px;
      font-size: 0.9rem;
      font-weight: 600;
    }

    .unlocked-tier-badge {
      background: linear-gradient(45deg, #17a2b8, #138496);
      color: #fff;
      padding: 0.3rem 1rem;
      border-radius: 15px;
      font-size: 0.9rem;
      font-weight: 600;
    }

    .tier-btn.current {
      background: rgba(0, 255, 0, 0.2);
      color: #00ff00;
      cursor: not-allowed;
      border: 1px solid rgba(0, 255, 0, 0.3);
    }

    .tier-btn.unlocked {
      background: rgba(23, 162, 184, 0.2);
      color: #17a2b8;
      cursor: not-allowed;
      border: 1px solid rgba(23, 162, 184, 0.3);
    }

    .membership-tier.current-tier {
      border: 2px solid rgba(0, 255, 0, 0.5);
      background: rgba(0, 255, 0, 0.05);
    }

    .membership-tier.unlocked-tier {
      border: 2px solid rgba(23, 162, 184, 0.5);
      background: rgba(23, 162, 184, 0.05);
    }

    /* ===== POLL STYLES ===== */
    .poll-container {
      max-width: 400px;
      margin: 0 auto;
      padding: 1rem;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      border: 1px solid rgba(255, 215, 0, 0.2);
    }

    .poll-question {
      font-family: 'Cinzel', serif;
      font-size: 1rem;
      color: #ffd700;
      text-align: center;
      margin-bottom: 1rem;
    }

    .poll-option {
      margin-bottom: 0.8rem;
      padding: 0.8rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .poll-option:hover {
      background: rgba(255, 215, 0, 0.1);
      border-color: rgba(255, 215, 0, 0.3);
      transform: scale(1.02);
    }

    .poll-option-label {
      font-family: 'Inter', sans-serif;
      font-size: 0.9rem;
      color: #fff;
      margin-bottom: 0.5rem;
    }

    .poll-option-bar {
      height: 6px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 0.3rem;
    }

    .poll-option-progress {
      height: 100%;
      background: linear-gradient(90deg, #ffd700, #ffed4e);
      border-radius: 3px;
      transition: width 0.5s ease;
    }

    .poll-option-stats {
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      color: rgba(255, 255, 255, 0.7);
    }

    /* ===== DONATION LINKS ===== */
    .donation-links {
      text-align: center;
      margin-top: 3rem;
      padding-top: 2rem;
      border-top: 1px solid rgba(255, 215, 0, 0.2);
    }

    .donation-title {
      font-family: 'Cinzel', serif;
      font-size: 1.2rem;
      color: #ffd700;
      margin-bottom: 1rem;
    }

    .donation-buttons {
      display: flex;
      justify-content: center;
      gap: 2rem;
      flex-wrap: wrap;
    }

    .donation-btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.8rem 1.5rem;
      text-decoration: none;
      border-radius: 10px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .donation-btn.paypal {
      background: rgba(0, 48, 135, 0.8);
      color: #fff;
      border: 1px solid rgba(0, 48, 135, 0.5);
    }

    .donation-btn.paypal:hover {
      background: rgba(0, 48, 135, 1);
      transform: translateY(-2px);
    }

    .donation-btn.coinbase {
      background: rgba(0, 82, 255, 0.8);
      color: #fff;
      border: 1px solid rgba(0, 82, 255, 0.5);
    }

    .donation-btn.coinbase:hover {
      background: rgba(0, 82, 255, 1);
      transform: translateY(-2px);
    }

    /* ===== ANIMATIONS ===== */
    .fade-in-up {
      opacity: 0;
      transform: translateY(30px);
      animation: fadeInUp 0.8s ease forwards;
    }

    @keyframes fadeInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .float {
      animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    /* ===== RESPONSIVE ===== */
    @media (max-width: 768px) {
      .feature-sections {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .donation-buttons {
        flex-direction: column;
        align-items: center;
      }

      .membership-tiers {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div id="navbar-container"></div>

  <main>
    <!-- Version Info -->
    <section class="version-info">
      <h2 class="version-title">WC Arena - Blacksmith</h2>
      <p class="version-subtitle">Current Version 1.0</p>
    </section>

    <!-- Features and Poll Section -->
    <section class="glass-section fade-in-up">
      <div class="feature-sections">
        <!-- Current Features -->
        <div class="collapsible-section">
          <div class="collapsible-header" onclick="toggleCollapsible(this)">
            <span>🔥 Current Features</span>
            <i class="fas fa-chevron-down"></i>
          </div>
          <div class="collapsible-content">
            <ul class="feature-list">
              <li><i class="fas fa-trophy"></i> Competitive Ladder System</li>
              <li><i class="fas fa-users"></i> Real-time Player Matching</li>
              <li><i class="fas fa-chart-line"></i> Advanced Statistics Tracking</li>
              <li><i class="fas fa-medal"></i> Achievement System</li>
              <li><i class="fas fa-comments"></i> Integrated Chat System</li>
              <li><i class="fas fa-shield-alt"></i> Clan Support</li>
              <li><i class="fas fa-map"></i> Map Repository</li>
              <li><i class="fas fa-crown"></i> Profile Customization</li>
              <li><i class="fas fa-stream"></i> Stream Integration</li>
              <li><i class="fas fa-gamepad"></i> Tournament System</li>
            </ul>
          </div>
        </div>

        <!-- Upcoming Features -->
        <div class="collapsible-section">
          <div class="collapsible-header" onclick="toggleCollapsible(this)">
            <span>🚀 Upcoming v1.1</span>
            <i class="fas fa-chevron-down"></i>
          </div>
          <div class="collapsible-content">
            <ul class="feature-list">
              <li><i class="fas fa-robot"></i> AI-Powered Match Analysis</li>
              <li><i class="fas fa-mobile-alt"></i> Mobile App Companion</li>
              <li><i class="fas fa-video"></i> Replay System</li>
              <li><i class="fas fa-gift"></i> Seasonal Rewards</li>
              <li><i class="fas fa-globe"></i> Regional Rankings</li>
              <li><i class="fas fa-calendar"></i> Scheduled Tournaments</li>
              <li><i class="fas fa-cog"></i> Advanced Settings</li>
              <li><i class="fas fa-bell"></i> Push Notifications</li>
              <li><i class="fas fa-star"></i> Rating Improvements</li>
              <li><i class="fas fa-lock"></i> Anti-Cheat Enhancements</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Community Poll -->
      <div>
        <h3 style="font-family: 'Cinzel', serif; font-size: 1.8rem; color: #ffd700; text-align: center; margin-bottom: 1.5rem;">Community Voice</h3>
        <div class="poll-container">
          <div class="poll-question">Is my wife going to divorce me?</div>
          <div class="poll-options" id="poll-options">
            <div class="poll-option" data-option="yes">
              <div class="poll-option-label">Yes</div>
              <div class="poll-option-bar">
                <div class="poll-option-progress" style="width: 0%"></div>
              </div>
              <div class="poll-option-stats">
                <span class="poll-option-percentage">0%</span>
                <span class="poll-option-votes">0 votes</span>
              </div>
            </div>
            <div class="poll-option" data-option="no">
              <div class="poll-option-label">No</div>
              <div class="poll-option-bar">
                <div class="poll-option-progress" style="width: 0%"></div>
              </div>
              <div class="poll-option-stats">
                <span class="poll-option-percentage">0%</span>
                <span class="poll-option-votes">0 votes</span>
              </div>
            </div>
          </div>
          <div class="poll-results" id="poll-results" style="display: none;">
            <p style="color: rgba(255, 255, 255, 0.8); margin-top: 2rem;">
              Thanks for voting! Help avoid this problem by supporting the project.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Become a Hero Membership Section -->
    <section class="glass-section fade-in-up">
      <h2 class="section-title">🛡️ Become a Hero</h2>
      <p class="section-subtitle">
        Unlock exclusive profile avatars and support the realm with monthly subscriptions
      </p>
      
      <div id="membership-container" class="membership-showcase">


        <div class="membership-tiers" id="membership-tiers">
          <!-- Tiers will be loaded dynamically -->
        </div>

        <div class="membership-info" style="margin-top: 2rem; text-align: center; color: rgba(255, 255, 255, 0.8);">
          <p><i class="fas fa-shield-alt"></i> Secure monthly subscriptions with Square</p>
          <p><i class="fas fa-sync-alt"></i> Easy upgrade/downgrade anytime</p>
          <p><i class="fas fa-times-circle"></i> Cancel subscription anytime - no long-term commitment</p>
        </div>

        <!-- Donation Links -->
        <div class="donation-links">
          <div class="donation-title">One-time donations to support server costs</div>
          <div class="donation-buttons">
            <a href="https://www.paypal.com/paypalme/wolfandman" target="_blank" class="donation-btn paypal">
              <i class="fab fa-paypal"></i>
              <span>PayPal Donation</span>
            </a>
            <a href="https://commerce.coinbase.com/checkout/fce0e325-d5b9-4b8c-a270-5508bdde7eeb" target="_blank" class="donation-btn coinbase">
              <i class="fab fa-bitcoin"></i>
              <span>Coinbase Donation</span>
            </a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <div id="footer-container"></div>

  <!-- Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/modules/MembershipManager.js"></script>
  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <script src="/js/utils.js"></script>

  <script>
    // Collapsible sections functionality
    function toggleCollapsible(header) {
      const content = header.nextElementSibling;
      const icon = header.querySelector('i');
      
      content.classList.toggle('expanded');
      
      if (content.classList.contains('expanded')) {
        icon.style.transform = 'rotate(180deg)';
      } else {
        icon.style.transform = 'rotate(0deg)';
      }
    }

    // Intersection Observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.animationDelay = `${Math.random() * 0.5}s`;
          entry.target.classList.add('fade-in-up');
        }
      });
    }, observerOptions);

    // Observe all sections
    document.addEventListener('DOMContentLoaded', () => {
      document.querySelectorAll('.glass-section').forEach(section => {
        observer.observe(section);
      });
    });

    /**
     * Update poll display with current data
     */
    function updatePollDisplay(pollData) {
      const pollOptions = document.querySelectorAll('.poll-option');
      if (!pollOptions.length) return;

      const total = pollData.yes + pollData.no;
      if (total > 0) {
        const yesPercent = Math.round((pollData.yes / total) * 100);
        const noPercent = Math.round((pollData.no / total) * 100);

        // Update yes option
        const yesOption = document.querySelector('.poll-option[data-option="yes"]');
        if (yesOption) {
          yesOption.querySelector('.poll-option-progress').style.width = `${yesPercent}%`;
          yesOption.querySelector('.poll-option-percentage').textContent = `${yesPercent}%`;
          yesOption.querySelector('.poll-option-votes').textContent = `${pollData.yes} votes`;
        }

        // Update no option
        const noOption = document.querySelector('.poll-option[data-option="no"]');
        if (noOption) {
          noOption.querySelector('.poll-option-progress').style.width = `${noPercent}%`;
          noOption.querySelector('.poll-option-percentage').textContent = `${noPercent}%`;
          noOption.querySelector('.poll-option-votes').textContent = `${pollData.no} votes`;
        }
      }
    }

    /**
     * Show poll results
     */
    function showResults() {
      const pollResults = document.getElementById('poll-results');
      if (!pollResults) return;
      
      pollResults.style.display = 'block';

      // Make options non-clickable
      const pollOptions = document.querySelectorAll('.poll-option');
      pollOptions.forEach(option => {
        option.style.cursor = 'default';
        option.classList.add('voted');
        option.style.opacity = '0.7';
      });
    }

    /**
     * Initialize poll functionality
     */
    async function initializePoll() {
      let pollData = {
        yes: 0,
        no: 0,
        hasVoted: false
      };

      try {
        const response = await fetch('/api/poll/divorce', {
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          if (data && data.options) {
            pollData = {
              yes: data.options.find(opt => opt.value === 'yes')?.votes || 0,
              no: data.options.find(opt => opt.value === 'no')?.votes || 0,
              hasVoted: data.hasVoted || false
            };
          }
        }
      } catch (err) {
        console.error('Error loading poll data:', err);
      }

      // Update the display with current data
      updatePollDisplay(pollData);

      // Show results if user has already voted
      if (pollData.hasVoted) {
        showResults();
        return;
      }

      // Add click handlers for voting
      const pollOptions = document.querySelectorAll('.poll-option');
      pollOptions.forEach(option => {
        option.addEventListener('click', async () => {
          if (pollData.hasVoted) return;

          const vote = option.dataset.option;
          try {
            const response = await fetch('/api/poll/vote', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ poll: 'divorce', vote }),
              credentials: 'include'
            });

            if (!response.ok) {
              throw new Error('Failed to save vote');
            }

            const data = await response.json();
            if (data.poll && data.poll.options) {
              pollData = {
                yes: data.poll.options.find(opt => opt.value === 'yes')?.votes || 0,
                no: data.poll.options.find(opt => opt.value === 'no')?.votes || 0,
                hasVoted: true
              };
              updatePollDisplay(pollData);
              showResults();
            }
          } catch (err) {
            console.error('Error sending vote to server:', err);
          }
        });
      });
    }

    // Wait for DOM to load before initializing components
    document.addEventListener('DOMContentLoaded', async () => {
      // Initialize all components
      await Promise.all([
        initializePoll(),
        membershipManager.init()
      ]).catch(error => console.error('Error initializing components:', error));
    });
  </script>
</body>
</html>
